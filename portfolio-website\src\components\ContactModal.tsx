'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Mail, Calendar, MessageSquare, Send, Phone } from 'lucide-react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { cn } from '@/lib/utils';

interface ContactModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export function ContactModal({ isOpen, onClose }: ContactModalProps) {
  const [activeTab, setActiveTab] = useState<'options' | 'form'>('options');
  const { register, handleSubmit, formState: { errors }, reset } = useForm<ContactFormData>();

  const handleEmailClick = () => {
    window.open('mailto:<EMAIL>?subject=Let\'s Connect!', '_blank');
    onClose();
  };

  const handleCallClick = () => {
    window.open('https://calendly.com/denis-ai-specialist/30min', '_blank');
    onClose();
  };

  const handleFormSubmit = async (data: ContactFormData) => {
    try {
      // Simulate form submission
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('Message sent successfully! I\'ll get back to you soon.');
      reset();
      onClose();
    } catch (error) {
      toast.error('Failed to send message. Please try again.');
    }
  };

  const contactOptions = [
    {
      id: 'email',
      title: 'Send an Email',
      description: 'Quick and direct communication',
      icon: Mail,
      action: handleEmailClick,
      color: 'from-blue-500 to-cyan-500',
    },
    {
      id: 'call',
      title: 'Book a 30 Min Call',
      description: 'Let\'s discuss your project in detail',
      icon: Calendar,
      action: handleCallClick,
      color: 'from-green-500 to-emerald-500',
    },
    {
      id: 'form',
      title: 'Contact Form',
      description: 'Tell me about your needs',
      icon: MessageSquare,
      action: () => setActiveTab('form'),
      color: 'from-purple-500 to-pink-500',
    },
  ];

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          transition={{ type: "spring", duration: 0.5 }}
          className="bg-white dark:bg-dark-card rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="relative p-6 border-b border-gray-200 dark:border-gray-700">
            <button
              onClick={onClose}
              className="absolute top-4 right-4 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
            </button>
            
            <div className="text-center">
              <h2 className="text-2xl font-bold text-light-h2 dark:text-dark-h2 mb-2">
                We'd Love to Hear From You!
              </h2>
              <p className="text-light-secondary dark:text-dark-secondary">
                {activeTab === 'options' 
                  ? "Choose how you'd like to connect:" 
                  : "Tell us about your project"
                }
              </p>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            {activeTab === 'options' ? (
              <div className="space-y-4">
                {contactOptions.map((option, index) => (
                  <motion.button
                    key={option.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    onClick={option.action}
                    className="w-full p-4 rounded-xl border-2 border-gray-200 dark:border-gray-700 hover:border-light-primary dark:hover:border-dark-primary transition-all duration-300 hover:shadow-lg group"
                  >
                    <div className="flex items-center space-x-4">
                      <div className={cn(
                        "w-12 h-12 rounded-lg bg-gradient-to-br flex items-center justify-center",
                        option.color
                      )}>
                        <option.icon className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1 text-left">
                        <h3 className="font-semibold text-light-h3 dark:text-dark-h3 group-hover:text-light-primary dark:group-hover:text-dark-primary transition-colors">
                          {option.title}
                        </h3>
                        <p className="text-sm text-light-secondary dark:text-dark-secondary">
                          {option.description}
                        </p>
                      </div>
                    </div>
                  </motion.button>
                ))}
              </div>
            ) : (
              <motion.form
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                onSubmit={handleSubmit(handleFormSubmit)}
                className="space-y-4"
              >
                <button
                  type="button"
                  onClick={() => setActiveTab('options')}
                  className="flex items-center text-sm text-light-primary dark:text-dark-primary hover:underline mb-4"
                >
                  ← Back to options
                </button>

                <div>
                  <label className="block text-sm font-medium text-light-body dark:text-dark-body mb-2">
                    Name *
                  </label>
                  <input
                    {...register('name', { required: 'Name is required' })}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors"
                    placeholder="Your full name"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-500">{errors.name.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-light-body dark:text-dark-body mb-2">
                    Email *
                  </label>
                  <input
                    {...register('email', { 
                      required: 'Email is required',
                      pattern: {
                        value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                        message: 'Please enter a valid email'
                      }
                    })}
                    type="email"
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors"
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-500">{errors.email.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-light-body dark:text-dark-body mb-2">
                    Subject *
                  </label>
                  <input
                    {...register('subject', { required: 'Subject is required' })}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors"
                    placeholder="What's this about?"
                  />
                  {errors.subject && (
                    <p className="mt-1 text-sm text-red-500">{errors.subject.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-light-body dark:text-dark-body mb-2">
                    Message *
                  </label>
                  <textarea
                    {...register('message', { required: 'Message is required' })}
                    rows={4}
                    className="w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors resize-none"
                    placeholder="Tell me about your project or how I can help..."
                  />
                  {errors.message && (
                    <p className="mt-1 text-sm text-red-500">{errors.message.message}</p>
                  )}
                </div>

                <button
                  type="submit"
                  className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-medium rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105"
                >
                  <Send className="w-4 h-4" />
                  <span>Send Message</span>
                </button>
              </motion.form>
            )}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
