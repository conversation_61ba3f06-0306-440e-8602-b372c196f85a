'use client';

import { useState } from 'react';
import { X, Mail, Calendar, MessageSquare, Send } from 'lucide-react';

interface ContactModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ContactModal({ isOpen, onClose }: ContactModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4" onClick={onClose}>
      <div
        className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="relative p-6 border-b border-gray-200">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>

          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              We'd Love to Hear From You!
            </h2>
            <p className="text-gray-600">
              Choose how you'd like to connect:
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="space-y-4">
            <button
              onClick={() => {
                window.open('mailto:<EMAIL>?subject=Let\'s Connect!', '_blank');
                onClose();
              }}
              className="w-full p-4 rounded-xl border-2 border-gray-200 hover:border-blue-500 transition-all duration-300 hover:shadow-lg group"
            >
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center">
                  <Mail className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1 text-left">
                  <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    Send an Email
                  </h3>
                  <p className="text-sm text-gray-600">
                    Quick and direct communication
                  </p>
                </div>
              </div>
            </button>

            <button
              onClick={() => {
                window.open('https://calendly.com/denis-ai-specialist/30min', '_blank');
                onClose();
              }}
              className="w-full p-4 rounded-xl border-2 border-gray-200 hover:border-blue-500 transition-all duration-300 hover:shadow-lg group"
            >
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center">
                  <Calendar className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1 text-left">
                  <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    Book a 30 Min Call
                  </h3>
                  <p className="text-sm text-gray-600">
                    Let's discuss your project in detail
                  </p>
                </div>
              </div>
            </button>

            <button
              onClick={() => {
                // For now, just show an alert - can implement form later
                alert('Contact form coming soon! Please use email or calendar for now.');
              }}
              className="w-full p-4 rounded-xl border-2 border-gray-200 hover:border-blue-500 transition-all duration-300 hover:shadow-lg group"
            >
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                  <MessageSquare className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1 text-left">
                  <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    Contact Form
                  </h3>
                  <p className="text-sm text-gray-600">
                    Tell me about your needs
                  </p>
                </div>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
