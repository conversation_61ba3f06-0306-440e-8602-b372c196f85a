'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import { Mail, Phone, MapPin, Send, Linkedin, Twitter, Github, Globe, ArrowUp } from 'lucide-react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';

interface NewsletterFormData {
  email: string;
}

const quickLinks = [
  { label: 'Home', href: '#home' },
  { label: 'Services', href: '#services' },
  { label: 'Portfolio', href: '#portfolio' },
  { label: 'About', href: '#story' },
  { label: 'Case Studies', href: '#case-studies' },
  { label: 'Testimonials', href: '#testimonials' },
];

const resources = [
  { label: 'Blog', href: '#insights' },
  { label: 'AI Insights', href: '#insights' },
  { label: 'Automation Guide', href: '#' },
  { label: 'Best Practices', href: '#' },
  { label: 'Industry Reports', href: '#' },
  { label: 'Whitepapers', href: '#' },
];

const support = [
  { label: 'FAQ', href: '#faq' },
  { label: 'Contact', href: '#contact' },
  { label: 'Book a Call', href: '#' },
  { label: 'Project Inquiry', href: '#' },
  { label: 'Technical Support', href: '#' },
  { label: 'Documentation', href: '#' },
];

const socialLinks = [
  { name: 'LinkedIn', icon: Linkedin, href: 'https://linkedin.com/in/denis-ai-specialist', color: 'hover:text-blue-600' },
  { name: 'Twitter', icon: Twitter, href: 'https://twitter.com/denis_ai', color: 'hover:text-blue-400' },
  { name: 'GitHub', icon: Github, href: 'https://github.com/denis-ai', color: 'hover:text-gray-600' },
  { name: 'Website', icon: Globe, href: 'https://denis-portfolio.com', color: 'hover:text-green-600' },
];

export function Footer() {
  const { register, handleSubmit, formState: { errors }, reset } = useForm<NewsletterFormData>();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleNewsletterSubmit = async (data: NewsletterFormData) => {
    setIsSubmitting(true);
    try {
      // Simulate newsletter subscription
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Successfully subscribed to newsletter!');
      reset();
    } catch (error) {
      toast.error('Failed to subscribe. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToSection = (href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <footer className="bg-light-card dark:bg-dark-card border-t border-light-primary/20 dark:border-dark-primary/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
            {/* Brand Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="lg:col-span-1"
            >
              <div className="mb-6">
                <h3 className="text-2xl font-bold gradient-text mb-4">DENIS</h3>
                <p className="text-light-body dark:text-dark-body leading-relaxed mb-6">
                  Transforming businesses worldwide with cutting-edge AI and automation solutions. 
                  From Kenya to global success, delivering results that matter.
                </p>
              </div>

              {/* Contact Info */}
              <div className="space-y-3">
                <div className="flex items-center text-light-secondary dark:text-dark-secondary">
                  <Mail className="w-4 h-4 mr-3 text-light-primary dark:text-dark-primary" />
                  <span className="text-sm"><EMAIL></span>
                </div>
                <div className="flex items-center text-light-secondary dark:text-dark-secondary">
                  <Phone className="w-4 h-4 mr-3 text-light-primary dark:text-dark-primary" />
                  <span className="text-sm">+254 700 000 000</span>
                </div>
                <div className="flex items-center text-light-secondary dark:text-dark-secondary">
                  <MapPin className="w-4 h-4 mr-3 text-light-primary dark:text-dark-primary" />
                  <span className="text-sm">Nairobi, Kenya</span>
                </div>
              </div>

              {/* Social Links */}
              <div className="flex space-x-4 mt-6">
                {socialLinks.map((social) => (
                  <a
                    key={social.name}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`p-2 rounded-lg bg-light-background dark:bg-dark-background text-light-secondary dark:text-dark-secondary ${social.color} transition-all duration-300 hover:scale-110`}
                    aria-label={social.name}
                  >
                    <social.icon className="w-5 h-5" />
                  </a>
                ))}
              </div>
            </motion.div>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <h4 className="text-lg font-semibold text-light-h3 dark:text-dark-h3 mb-6">
                Quick Links
              </h4>
              <ul className="space-y-3">
                {quickLinks.map((link) => (
                  <li key={link.label}>
                    <button
                      onClick={() => scrollToSection(link.href)}
                      className="text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary transition-colors duration-300 text-sm"
                    >
                      {link.label}
                    </button>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Resources */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h4 className="text-lg font-semibold text-light-h3 dark:text-dark-h3 mb-6">
                Resources
              </h4>
              <ul className="space-y-3">
                {resources.map((link) => (
                  <li key={link.label}>
                    <button
                      onClick={() => scrollToSection(link.href)}
                      className="text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary transition-colors duration-300 text-sm"
                    >
                      {link.label}
                    </button>
                  </li>
                ))}
              </ul>
            </motion.div>

            {/* Newsletter Signup */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <h4 className="text-lg font-semibold text-light-h3 dark:text-dark-h3 mb-6">
                Stay Updated
              </h4>
              <p className="text-light-secondary dark:text-dark-secondary text-sm mb-4">
                Get the latest insights on AI and automation delivered to your inbox.
              </p>
              
              <form onSubmit={handleSubmit(handleNewsletterSubmit)} className="space-y-3">
                <div>
                  <input
                    {...register('email', {
                      required: 'Email is required',
                      pattern: {
                        value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                        message: 'Please enter a valid email'
                      }
                    })}
                    type="email"
                    placeholder="Enter your email"
                    className="w-full px-4 py-3 rounded-lg border border-light-primary/20 dark:border-dark-primary/20 bg-light-background dark:bg-dark-background text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors text-sm"
                  />
                  {errors.email && (
                    <p className="mt-1 text-xs text-red-500">{errors.email.message}</p>
                  )}
                </div>
                
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full flex items-center justify-center px-4 py-3 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-medium rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                >
                  {isSubmitting ? (
                    'Subscribing...'
                  ) : (
                    <>
                      Subscribe
                      <Send className="ml-2 w-4 h-4" />
                    </>
                  )}
                </button>
              </form>

              {/* Support Links */}
              <div className="mt-8">
                <h5 className="text-sm font-medium text-light-h3 dark:text-dark-h3 mb-3">
                  Support
                </h5>
                <ul className="space-y-2">
                  {support.slice(0, 3).map((link) => (
                    <li key={link.label}>
                      <button
                        onClick={() => scrollToSection(link.href)}
                        className="text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary transition-colors duration-300 text-xs"
                      >
                        {link.label}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Bottom Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
          className="py-8 border-t border-light-primary/20 dark:border-dark-primary/20"
        >
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-light-secondary dark:text-dark-secondary text-sm mb-4 md:mb-0">
              © 2024 Denis AI & Automation Specialist. All rights reserved.
            </div>
            
            <div className="flex items-center space-x-6">
              <button
                onClick={() => scrollToSection('#')}
                className="text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary transition-colors duration-300 text-sm"
              >
                Privacy Policy
              </button>
              <button
                onClick={() => scrollToSection('#')}
                className="text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary transition-colors duration-300 text-sm"
              >
                Terms of Service
              </button>
              <button
                onClick={scrollToTop}
                className="p-2 rounded-lg bg-light-primary dark:bg-dark-primary text-white hover:shadow-neon transition-all duration-300 hover:scale-110"
                aria-label="Back to top"
              >
                <ArrowUp className="w-4 h-4" />
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </footer>
  );
}
