export interface TestimonialCard {
  id: string;
  type: 'text' | 'video' | 'stat' | 'quote';
  size: 'small' | 'medium' | 'large';
  accent?: 'gold' | 'green';
  content: {
    text?: string;
    stat?: string;
    metric?: string;
    quote?: string;
  };
  author: {
    name: string;
    role: string;
    avatar?: string;
    initials?: string;
  };
  video?: {
    thumbnail?: string;
    onClick?: () => void;
  };
}

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  readTime: string;
  category: string;
  image: string;
  slug: string;
  publishedAt: string;
}

export interface CaseStudy {
  id: string;
  title: string;
  description: string;
  metrics: {
    label: string;
    value: string;
    improvement: string;
  }[];
  timeline: string;
  image: string;
  slug: string;
}

export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category?: string;
}

export interface ContactOption {
  id: string;
  title: string;
  description: string;
  icon: string;
  action: () => void;
  href?: string;
}

export interface SocialLink {
  id: string;
  name: string;
  url: string;
  icon: string;
}

export interface NavigationItem {
  id: string;
  label: string;
  href: string;
  isActive?: boolean;
}

export interface ThemeConfig {
  theme: 'light' | 'dark';
  setTheme: (theme: 'light' | 'dark') => void;
}

export interface HeroContent {
  headline: string;
  subheadline: string;
  ctaPrimary: {
    text: string;
    href: string;
  };
  ctaSecondary: {
    text: string;
    href: string;
  };
  personalImage: string;
  platformIcons: string[];
}

export interface PersonalStory {
  title: string;
  subtitle: string;
  content: string[];
  image: string;
  highlights: string[];
}
