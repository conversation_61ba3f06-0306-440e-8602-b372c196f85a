'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Minus, HelpCircle } from 'lucide-react';
import type { FAQItem } from '@/types';

const faqData: FAQItem[] = [
  {
    id: '1',
    question: 'How do you approach AI and automation projects?',
    answer: 'I start with a comprehensive analysis of your current processes to identify automation opportunities. Then I design custom AI solutions that integrate seamlessly with your existing systems, ensuring minimal disruption while maximizing efficiency gains. Every project includes thorough testing, training, and ongoing support.',
    category: 'Process'
  },
  {
    id: '2',
    question: 'What makes your AI solutions different from others?',
    answer: 'My solutions are built with a focus on practical business outcomes rather than just technical complexity. I combine cutting-edge AI technologies with deep business understanding, ensuring that every automation delivers measurable ROI. Plus, I provide comprehensive training and documentation so your team can confidently manage the systems.',
    category: 'Approach'
  },
  {
    id: '3',
    question: 'How long does a typical AI automation project take?',
    answer: 'Project timelines vary based on complexity, but most automation projects are completed within 2-8 weeks. Simple workflow automations can be delivered in 1-2 weeks, while complex AI systems may take 2-3 months. I always provide detailed timelines upfront and keep you updated throughout the process.',
    category: 'Timeline'
  },
  {
    id: '4',
    question: 'Do you provide ongoing support after project completion?',
    answer: 'Absolutely! I offer comprehensive post-launch support including system monitoring, performance optimization, user training, and technical assistance. Most clients choose ongoing maintenance packages to ensure their AI systems continue performing optimally as their business grows.',
    category: 'Support'
  },
  {
    id: '5',
    question: 'Can you work with international clients and different time zones?',
    answer: 'Yes, I work with clients globally and am experienced in managing projects across different time zones. I use collaborative tools and maintain flexible communication schedules to ensure smooth project delivery regardless of location. Many of my successful projects have been with international teams.',
    category: 'International'
  },
  {
    id: '6',
    question: 'What industries do you specialize in?',
    answer: 'I have experience across various industries including e-commerce, healthcare, finance, marketing agencies, and SaaS companies. My approach is industry-agnostic - I focus on understanding your specific business processes and challenges to create tailored solutions that work for your unique context.',
    category: 'Industries'
  },
  {
    id: '7',
    question: 'How do you ensure data security and privacy?',
    answer: 'Data security is paramount in all my projects. I implement industry-standard encryption, secure API connections, and follow best practices for data handling. All solutions are designed with privacy by design principles, and I can work within your existing security frameworks and compliance requirements.',
    category: 'Security'
  },
  {
    id: '8',
    question: 'What\'s the typical ROI for AI automation projects?',
    answer: 'Most clients see ROI within 3-6 months, with typical returns ranging from 200-500% in the first year. The exact ROI depends on the processes being automated, but common benefits include 40-80% time savings, reduced errors, improved customer satisfaction, and the ability to scale operations without proportional staff increases.',
    category: 'ROI'
  }
];

interface FAQItemProps {
  faq: FAQItem;
  isOpen: boolean;
  onToggle: () => void;
  index: number;
}

function FAQItemComponent({ faq, isOpen, onToggle, index }: FAQItemProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className="bg-white dark:bg-dark-card rounded-2xl shadow-card hover:shadow-card-hover transition-all duration-300"
    >
      <button
        onClick={onToggle}
        className="w-full p-6 text-left flex items-center justify-between hover:bg-light-primary/5 dark:hover:bg-dark-primary/5 rounded-2xl transition-colors duration-300"
      >
        <h3 className="text-lg font-semibold text-light-h2 dark:text-dark-h2 pr-4">
          {faq.question}
        </h3>
        <div className="flex-shrink-0">
          {isOpen ? (
            <Minus className="w-6 h-6 text-light-primary dark:text-dark-primary" />
          ) : (
            <Plus className="w-6 h-6 text-light-primary dark:text-dark-primary" />
          )}
        </div>
      </button>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className="overflow-hidden"
          >
            <div className="px-6 pb-6">
              <div className="pt-2 border-t border-light-primary/10 dark:border-dark-primary/10">
                <p className="text-light-body dark:text-dark-body leading-relaxed">
                  {faq.answer}
                </p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

export function FAQ() {
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());

  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  const categories = Array.from(new Set(faqData.map(faq => faq.category)));

  return (
    <section id="faq" className="py-20 bg-light-background dark:bg-dark-background">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <span className="inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4">
            ❓ Got Questions?
          </span>
          
          <h2 className="text-4xl sm:text-5xl font-bold gradient-text mb-6">
            Frequently Asked Questions
          </h2>
          
          <p className="text-lg text-light-secondary dark:text-dark-secondary max-w-3xl mx-auto">
            Everything you need to know about working with me and my AI automation services. 
            Can't find what you're looking for? Feel free to reach out!
          </p>
        </motion.div>

        {/* FAQ Categories */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center gap-3 mb-12"
        >
          {categories.map((category) => (
            <span
              key={category}
              className="px-4 py-2 bg-light-card dark:bg-dark-card text-light-secondary dark:text-dark-secondary rounded-full text-sm border border-light-primary/20 dark:border-dark-primary/20"
            >
              {category}
            </span>
          ))}
        </motion.div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {faqData.map((faq, index) => (
            <FAQItemComponent
              key={faq.id}
              faq={faq}
              isOpen={openItems.has(faq.id)}
              onToggle={() => toggleItem(faq.id)}
              index={index}
            />
          ))}
        </div>

        {/* Contact CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16 pt-16 border-t border-light-primary/20 dark:border-dark-primary/20"
        >
          <div className="bg-gradient-to-r from-light-primary/10 to-light-primary/5 dark:from-dark-primary/10 dark:to-dark-primary/5 rounded-3xl p-8">
            <HelpCircle className="w-12 h-12 text-light-primary dark:text-dark-primary mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-light-h2 dark:text-dark-h2 mb-4">
              Still Have Questions?
            </h3>
            <p className="text-light-secondary dark:text-dark-secondary mb-6 max-w-2xl mx-auto">
              I'm here to help! Whether you need clarification on my services or want to discuss 
              your specific project requirements, don't hesitate to reach out.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="inline-flex items-center px-8 py-4 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-semibold rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105">
                Schedule a Call
              </button>
              <button className="inline-flex items-center px-8 py-4 bg-light-card dark:bg-dark-card border-2 border-light-primary dark:border-dark-primary text-light-primary dark:text-dark-primary font-semibold rounded-lg hover:bg-light-primary dark:hover:bg-dark-primary hover:text-white transition-all duration-300">
                Send a Message
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
