'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { Clock, ArrowRight, Tag } from 'lucide-react';
import type { BlogPost } from '@/types';

const blogPosts: BlogPost[] = [
  {
    id: '1',
    title: 'The Future of AI in Business Automation',
    excerpt: 'Discover how artificial intelligence is revolutionizing business processes and creating unprecedented opportunities for growth and efficiency.',
    readTime: '5 min read',
    category: 'AI Strategy',
    image: '/placeholder.png',
    slug: 'future-ai-business-automation',
    publishedAt: '2024-01-15',
  },
  {
    id: '2',
    title: 'Building Scalable Automation Workflows',
    excerpt: 'Learn the essential principles and best practices for creating automation systems that grow with your business needs.',
    readTime: '7 min read',
    category: 'Automation',
    image: '/placeholder.png',
    slug: 'scalable-automation-workflows',
    publishedAt: '2024-01-10',
  },
  {
    id: '3',
    title: 'Data-Driven Decision Making with AI',
    excerpt: 'Transform your business intelligence with AI-powered analytics that provide actionable insights and predictive capabilities.',
    readTime: '6 min read',
    category: 'Data Science',
    image: '/placeholder.png',
    slug: 'data-driven-ai-decisions',
    publishedAt: '2024-01-05',
  },
];

const categoryColors = {
  'AI Strategy': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
  'Automation': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
  'Data Science': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
};

export function BlogSection() {
  return (
    <section id="insights" className="py-20 bg-light-background dark:bg-dark-background">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <span className="inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4">
            📚 Latest Insights
          </span>
          
          <h2 className="text-4xl sm:text-5xl font-bold gradient-text mb-6">
            Latest AI and Automation Insights
          </h2>
          
          <p className="text-lg text-light-secondary dark:text-dark-secondary max-w-3xl mx-auto">
            Stay ahead of the curve with cutting-edge insights, practical strategies, and real-world 
            applications of AI and automation technologies.
          </p>
        </motion.div>

        {/* Blog Cards Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.map((post, index) => (
            <motion.article
              key={post.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group bg-white dark:bg-dark-card rounded-2xl overflow-hidden shadow-card hover:shadow-card-hover transition-all duration-300 hover:-translate-y-2"
            >
              {/* Image */}
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={post.image}
                  alt={post.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                
                {/* Category Tag */}
                <div className="absolute top-4 left-4">
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                    categoryColors[post.category as keyof typeof categoryColors]
                  }`}>
                    <Tag className="w-3 h-3 mr-1" />
                    {post.category}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="flex items-center text-sm text-light-secondary dark:text-dark-secondary mb-3">
                  <Clock className="w-4 h-4 mr-1" />
                  {post.readTime}
                  <span className="mx-2">•</span>
                  <time dateTime={post.publishedAt}>
                    {new Date(post.publishedAt).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric',
                    })}
                  </time>
                </div>

                <h3 className="text-xl font-bold text-light-h2 dark:text-dark-h2 mb-3 group-hover:text-light-primary dark:group-hover:text-dark-primary transition-colors">
                  {post.title}
                </h3>

                <p className="text-light-secondary dark:text-dark-secondary mb-4 line-clamp-3">
                  {post.excerpt}
                </p>

                {/* Read More Link */}
                <button className="group/btn inline-flex items-center text-light-primary dark:text-dark-primary font-medium hover:text-light-primary-deep dark:hover:text-dark-primary-deep transition-colors">
                  Read More
                  <ArrowRight className="ml-2 w-4 h-4 group-hover/btn:translate-x-1 transition-transform" />
                </button>
              </div>
            </motion.article>
          ))}
        </div>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <button className="inline-flex items-center px-8 py-4 bg-light-card dark:bg-dark-card border-2 border-light-primary dark:border-dark-primary text-light-primary dark:text-dark-primary font-semibold rounded-lg hover:bg-light-primary dark:hover:bg-dark-primary hover:text-white transition-all duration-300 hover:scale-105">
            View All Articles
            <ArrowRight className="ml-2 w-5 h-5" />
          </button>
        </motion.div>
      </div>
    </section>
  );
}
