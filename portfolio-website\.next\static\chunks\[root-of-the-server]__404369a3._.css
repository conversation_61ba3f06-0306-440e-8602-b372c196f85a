/* [next]/internal/font/google/inter_9e72d27f.module.css [app-client] (css) */
@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n_wU-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n_wU-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n_wU-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n_wU-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n_wU-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n_wU-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url("../media/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Fallback;
  src: local(Arial);
  ascent-override: 90.44%;
  descent-override: 22.52%;
  line-gap-override: 0.0%;
  size-adjust: 107.12%;
}

.inter_9e72d27f-module__JKMi0a__className {
  font-family: Inter, Inter Fallback;
  font-style: normal;
}

.inter_9e72d27f-module__JKMi0a__variable {
  --font-inter: "Inter", "Inter Fallback";
}


/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-translate-x: 0;
      --tw-translate-y: 0;
      --tw-translate-z: 0;
      --tw-rotate-x: initial;
      --tw-rotate-y: initial;
      --tw-rotate-z: initial;
      --tw-skew-x: initial;
      --tw-skew-y: initial;
      --tw-space-y-reverse: 0;
      --tw-space-x-reverse: 0;
      --tw-border-style: solid;
      --tw-gradient-position: initial;
      --tw-gradient-from: #0000;
      --tw-gradient-via: #0000;
      --tw-gradient-to: #0000;
      --tw-gradient-stops: initial;
      --tw-gradient-via-stops: initial;
      --tw-gradient-from-position: 0%;
      --tw-gradient-via-position: 50%;
      --tw-gradient-to-position: 100%;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
      --tw-blur: initial;
      --tw-brightness: initial;
      --tw-contrast: initial;
      --tw-grayscale: initial;
      --tw-hue-rotate: initial;
      --tw-invert: initial;
      --tw-opacity: initial;
      --tw-saturate: initial;
      --tw-sepia: initial;
      --tw-drop-shadow: initial;
      --tw-drop-shadow-color: initial;
      --tw-drop-shadow-alpha: 100%;
      --tw-drop-shadow-size: initial;
      --tw-backdrop-blur: initial;
      --tw-backdrop-brightness: initial;
      --tw-backdrop-contrast: initial;
      --tw-backdrop-grayscale: initial;
      --tw-backdrop-hue-rotate: initial;
      --tw-backdrop-invert: initial;
      --tw-backdrop-opacity: initial;
      --tw-backdrop-saturate: initial;
      --tw-backdrop-sepia: initial;
      --tw-duration: initial;
      --tw-ease: initial;
      --tw-scale-x: 1;
      --tw-scale-y: 1;
      --tw-scale-z: 1;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-red-500: oklch(63.7% .237 25.331);
    --color-orange-50: oklch(98% .016 73.684);
    --color-orange-900: oklch(40.8% .123 38.172);
    --color-yellow-50: oklch(98.7% .026 102.212);
    --color-yellow-400: oklch(85.2% .199 91.936);
    --color-yellow-900: oklch(42.1% .095 57.708);
    --color-green-50: oklch(98.2% .018 155.826);
    --color-green-100: oklch(96.2% .044 156.743);
    --color-green-300: oklch(87.1% .15 154.449);
    --color-green-400: oklch(79.2% .209 151.711);
    --color-green-500: oklch(72.3% .219 149.579);
    --color-green-600: oklch(62.7% .194 149.214);
    --color-green-800: oklch(44.8% .119 151.328);
    --color-green-900: oklch(39.3% .095 152.535);
    --color-emerald-50: oklch(97.9% .021 166.113);
    --color-emerald-500: oklch(69.6% .17 162.48);
    --color-emerald-900: oklch(37.8% .077 168.94);
    --color-cyan-50: oklch(98.4% .019 200.873);
    --color-cyan-100: oklch(95.6% .045 203.388);
    --color-cyan-500: oklch(71.5% .143 215.221);
    --color-cyan-600: oklch(60.9% .126 221.723);
    --color-blue-50: oklch(97% .014 254.604);
    --color-blue-100: oklch(93.2% .032 255.585);
    --color-blue-300: oklch(80.9% .105 251.813);
    --color-blue-400: oklch(70.7% .165 254.624);
    --color-blue-500: oklch(62.3% .214 259.815);
    --color-blue-600: oklch(54.6% .245 262.881);
    --color-blue-700: oklch(48.8% .243 264.376);
    --color-blue-800: oklch(42.4% .199 265.638);
    --color-blue-900: oklch(37.9% .146 265.522);
    --color-purple-100: oklch(94.6% .033 307.174);
    --color-purple-300: oklch(82.7% .119 306.383);
    --color-purple-500: oklch(62.7% .265 303.9);
    --color-purple-800: oklch(43.8% .218 303.724);
    --color-purple-900: oklch(38.1% .176 304.987);
    --color-pink-500: oklch(65.6% .241 354.308);
    --color-gray-50: oklch(98.5% .002 247.839);
    --color-gray-100: oklch(96.7% .003 264.542);
    --color-gray-200: oklch(92.8% .006 264.531);
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-500: oklch(55.1% .027 264.364);
    --color-gray-600: oklch(44.6% .03 256.802);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-gray-900: oklch(21% .034 264.665);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-md: 28rem;
    --container-lg: 32rem;
    --container-2xl: 42rem;
    --container-3xl: 48rem;
    --container-4xl: 56rem;
    --container-7xl: 80rem;
    --text-xs: .75rem;
    --text-xs--line-height: calc(1 / .75);
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-xl: 1.25rem;
    --text-xl--line-height: calc(1.75 / 1.25);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-3xl: 1.875rem;
    --text-3xl--line-height: calc(2.25 / 1.875);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --leading-tight: 1.25;
    --leading-relaxed: 1.625;
    --radius-lg: .5rem;
    --radius-xl: .75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --ease-out: cubic-bezier(0, 0, .2, 1);
    --animate-bounce: bounce 1s infinite;
    --blur-sm: 8px;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .absolute {
    position: absolute;
  }

  .fixed {
    position: fixed;
  }

  .relative {
    position: relative;
  }

  .inset-0 {
    inset: calc(var(--spacing) * 0);
  }

  .-top-4 {
    top: calc(var(--spacing) * -4);
  }

  .-top-6 {
    top: calc(var(--spacing) * -6);
  }

  .top-0 {
    top: calc(var(--spacing) * 0);
  }

  .top-1\/2 {
    top: 50%;
  }

  .top-4 {
    top: calc(var(--spacing) * 4);
  }

  .top-6 {
    top: calc(var(--spacing) * 6);
  }

  .top-8 {
    top: calc(var(--spacing) * 8);
  }

  .-right-4 {
    right: calc(var(--spacing) * -4);
  }

  .-right-6 {
    right: calc(var(--spacing) * -6);
  }

  .right-0 {
    right: calc(var(--spacing) * 0);
  }

  .right-4 {
    right: calc(var(--spacing) * 4);
  }

  .right-6 {
    right: calc(var(--spacing) * 6);
  }

  .-bottom-4 {
    bottom: calc(var(--spacing) * -4);
  }

  .-bottom-6 {
    bottom: calc(var(--spacing) * -6);
  }

  .bottom-0 {
    bottom: calc(var(--spacing) * 0);
  }

  .bottom-4 {
    bottom: calc(var(--spacing) * 4);
  }

  .bottom-6 {
    bottom: calc(var(--spacing) * 6);
  }

  .-left-4 {
    left: calc(var(--spacing) * -4);
  }

  .-left-6 {
    left: calc(var(--spacing) * -6);
  }

  .left-0 {
    left: calc(var(--spacing) * 0);
  }

  .left-4 {
    left: calc(var(--spacing) * 4);
  }

  .left-full {
    left: 100%;
  }

  .z-10 {
    z-index: 10;
  }

  .z-50 {
    z-index: 50;
  }

  .mx-2 {
    margin-inline: calc(var(--spacing) * 2);
  }

  .mx-4 {
    margin-inline: calc(var(--spacing) * 4);
  }

  .mx-auto {
    margin-inline: auto;
  }

  .mt-1 {
    margin-top: calc(var(--spacing) * 1);
  }

  .mt-2 {
    margin-top: calc(var(--spacing) * 2);
  }

  .mt-3 {
    margin-top: calc(var(--spacing) * 3);
  }

  .mt-4 {
    margin-top: calc(var(--spacing) * 4);
  }

  .mt-6 {
    margin-top: calc(var(--spacing) * 6);
  }

  .mt-8 {
    margin-top: calc(var(--spacing) * 8);
  }

  .mt-12 {
    margin-top: calc(var(--spacing) * 12);
  }

  .mt-16 {
    margin-top: calc(var(--spacing) * 16);
  }

  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }

  .mr-1 {
    margin-right: calc(var(--spacing) * 1);
  }

  .mr-2 {
    margin-right: calc(var(--spacing) * 2);
  }

  .mr-3 {
    margin-right: calc(var(--spacing) * 3);
  }

  .mb-1 {
    margin-bottom: calc(var(--spacing) * 1);
  }

  .mb-2 {
    margin-bottom: calc(var(--spacing) * 2);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-10 {
    margin-bottom: calc(var(--spacing) * 10);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .ml-2 {
    margin-left: calc(var(--spacing) * 2);
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .grid {
    display: grid;
  }

  .hidden {
    display: none;
  }

  .inline-block {
    display: inline-block;
  }

  .inline-flex {
    display: inline-flex;
  }

  .h-0\.5 {
    height: calc(var(--spacing) * .5);
  }

  .h-2 {
    height: calc(var(--spacing) * 2);
  }

  .h-3 {
    height: calc(var(--spacing) * 3);
  }

  .h-4 {
    height: calc(var(--spacing) * 4);
  }

  .h-5 {
    height: calc(var(--spacing) * 5);
  }

  .h-6 {
    height: calc(var(--spacing) * 6);
  }

  .h-8 {
    height: calc(var(--spacing) * 8);
  }

  .h-10 {
    height: calc(var(--spacing) * 10);
  }

  .h-12 {
    height: calc(var(--spacing) * 12);
  }

  .h-16 {
    height: calc(var(--spacing) * 16);
  }

  .h-20 {
    height: calc(var(--spacing) * 20);
  }

  .h-32 {
    height: calc(var(--spacing) * 32);
  }

  .h-40 {
    height: calc(var(--spacing) * 40);
  }

  .h-48 {
    height: calc(var(--spacing) * 48);
  }

  .h-64 {
    height: calc(var(--spacing) * 64);
  }

  .h-96 {
    height: calc(var(--spacing) * 96);
  }

  .h-full {
    height: 100%;
  }

  .max-h-\[90vh\] {
    max-height: 90vh;
  }

  .min-h-\[80vh\] {
    min-height: 80vh;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .w-1 {
    width: calc(var(--spacing) * 1);
  }

  .w-2 {
    width: calc(var(--spacing) * 2);
  }

  .w-3 {
    width: calc(var(--spacing) * 3);
  }

  .w-4 {
    width: calc(var(--spacing) * 4);
  }

  .w-5 {
    width: calc(var(--spacing) * 5);
  }

  .w-6 {
    width: calc(var(--spacing) * 6);
  }

  .w-8 {
    width: calc(var(--spacing) * 8);
  }

  .w-12 {
    width: calc(var(--spacing) * 12);
  }

  .w-16 {
    width: calc(var(--spacing) * 16);
  }

  .w-20 {
    width: calc(var(--spacing) * 20);
  }

  .w-full {
    width: 100%;
  }

  .max-w-2xl {
    max-width: var(--container-2xl);
  }

  .max-w-3xl {
    max-width: var(--container-3xl);
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-7xl {
    max-width: var(--container-7xl);
  }

  .max-w-lg {
    max-width: var(--container-lg);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .min-w-0 {
    min-width: calc(var(--spacing) * 0);
  }

  .flex-1 {
    flex: 1;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .grow {
    flex-grow: 1;
  }

  .translate-x-10 {
    --tw-translate-x: calc(var(--spacing) * 10);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .-translate-y-10 {
    --tw-translate-y: calc(var(--spacing) * -10);
    translate: var(--tw-translate-x) var(--tw-translate-y);
  }

  .transform {
    transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--tw-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, );
  }

  .animate-bounce {
    animation: var(--animate-bounce);
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .flex-col {
    flex-direction: column;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .justify-between {
    justify-content: space-between;
  }

  .justify-center {
    justify-content: center;
  }

  .gap-0 {
    gap: calc(var(--spacing) * 0);
  }

  .gap-2 {
    gap: calc(var(--spacing) * 2);
  }

  .gap-3 {
    gap: calc(var(--spacing) * 3);
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  .gap-6 {
    gap: calc(var(--spacing) * 6);
  }

  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }

  .gap-12 {
    gap: calc(var(--spacing) * 12);
  }

  .gap-16 {
    gap: calc(var(--spacing) * 16);
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-3 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-6 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-x-4 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-6 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));
  }

  :where(.space-x-8 > :not(:last-child)) {
    --tw-space-x-reverse: 0;
    margin-inline-start: calc(calc(var(--spacing) * 8) * var(--tw-space-x-reverse));
    margin-inline-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-x-reverse)));
  }

  .truncate {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  .overflow-hidden {
    overflow: hidden;
  }

  .rounded-2xl {
    border-radius: var(--radius-2xl);
  }

  .rounded-3xl {
    border-radius: var(--radius-3xl);
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .rounded-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-xl {
    border-radius: var(--radius-xl);
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-2 {
    border-style: var(--tw-border-style);
    border-width: 2px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-b {
    border-bottom-style: var(--tw-border-style);
    border-bottom-width: 1px;
  }

  .border-blue-600 {
    border-color: var(--color-blue-600);
  }

  .border-gray-100 {
    border-color: var(--color-gray-100);
  }

  .border-gray-200 {
    border-color: var(--color-gray-200);
  }

  .border-gray-300 {
    border-color: var(--color-gray-300);
  }

  .border-green-400 {
    border-color: var(--color-green-400);
  }

  .border-yellow-400 {
    border-color: var(--color-yellow-400);
  }

  .bg-black\/30 {
    background-color: #0000004d;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/30 {
      background-color: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
  }

  .bg-black\/50 {
    background-color: #00000080;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-black\/50 {
      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .bg-blue-50 {
    background-color: var(--color-blue-50);
  }

  .bg-blue-100 {
    background-color: var(--color-blue-100);
  }

  .bg-blue-600 {
    background-color: var(--color-blue-600);
  }

  .bg-cyan-500 {
    background-color: var(--color-cyan-500);
  }

  .bg-gray-50 {
    background-color: var(--color-gray-50);
  }

  .bg-gray-400 {
    background-color: var(--color-gray-400);
  }

  .bg-green-100 {
    background-color: var(--color-green-100);
  }

  .bg-purple-100 {
    background-color: var(--color-purple-100);
  }

  .bg-purple-500 {
    background-color: var(--color-purple-500);
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .bg-white\/95 {
    background-color: #fffffff2;
  }

  @supports (color: color-mix(in lab, red, red)) {
    .bg-white\/95 {
      background-color: color-mix(in oklab, var(--color-white) 95%, transparent);
    }
  }

  .bg-gradient-to-b {
    --tw-gradient-position: to bottom in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-br {
    --tw-gradient-position: to bottom right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-r {
    --tw-gradient-position: to right in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .bg-gradient-to-t {
    --tw-gradient-position: to top in oklab;
    background-image: linear-gradient(var(--tw-gradient-stops));
  }

  .from-black\/20 {
    --tw-gradient-from: #0003;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-black\/20 {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 20%, transparent);
    }
  }

  .from-black\/30 {
    --tw-gradient-from: #0000004d;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-black\/30 {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 30%, transparent);
    }
  }

  .from-black\/50 {
    --tw-gradient-from: #00000080;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  @supports (color: color-mix(in lab, red, red)) {
    .from-black\/50 {
      --tw-gradient-from: color-mix(in oklab, var(--color-black) 50%, transparent);
    }
  }

  .from-blue-50 {
    --tw-gradient-from: var(--color-blue-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-100 {
    --tw-gradient-from: var(--color-blue-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-500 {
    --tw-gradient-from: var(--color-blue-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-blue-600 {
    --tw-gradient-from: var(--color-blue-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-gray-800 {
    --tw-gradient-from: var(--color-gray-800);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-50 {
    --tw-gradient-from: var(--color-green-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-green-500 {
    --tw-gradient-from: var(--color-green-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-purple-500 {
    --tw-gradient-from: var(--color-purple-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .from-yellow-50 {
    --tw-gradient-from: var(--color-yellow-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .via-white {
    --tw-gradient-via: var(--color-white);
    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);
    --tw-gradient-stops: var(--tw-gradient-via-stops);
  }

  .to-cyan-50 {
    --tw-gradient-to: var(--color-cyan-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-cyan-100 {
    --tw-gradient-to: var(--color-cyan-100);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-cyan-500 {
    --tw-gradient-to: var(--color-cyan-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-cyan-600 {
    --tw-gradient-to: var(--color-cyan-600);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-emerald-50 {
    --tw-gradient-to: var(--color-emerald-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-emerald-500 {
    --tw-gradient-to: var(--color-emerald-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-gray-900 {
    --tw-gradient-to: var(--color-gray-900);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-orange-50 {
    --tw-gradient-to: var(--color-orange-50);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-pink-500 {
    --tw-gradient-to: var(--color-pink-500);
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .to-transparent {
    --tw-gradient-to: transparent;
    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
  }

  .bg-clip-text {
    background-clip: text;
  }

  .object-cover {
    object-fit: cover;
  }

  .p-2 {
    padding: calc(var(--spacing) * 2);
  }

  .p-4 {
    padding: calc(var(--spacing) * 4);
  }

  .p-6 {
    padding: calc(var(--spacing) * 6);
  }

  .p-8 {
    padding: calc(var(--spacing) * 8);
  }

  .px-3 {
    padding-inline: calc(var(--spacing) * 3);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .px-8 {
    padding-inline: calc(var(--spacing) * 8);
  }

  .py-1 {
    padding-block: calc(var(--spacing) * 1);
  }

  .py-2 {
    padding-block: calc(var(--spacing) * 2);
  }

  .py-2\.5 {
    padding-block: calc(var(--spacing) * 2.5);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-4 {
    padding-block: calc(var(--spacing) * 4);
  }

  .py-8 {
    padding-block: calc(var(--spacing) * 8);
  }

  .py-16 {
    padding-block: calc(var(--spacing) * 16);
  }

  .py-20 {
    padding-block: calc(var(--spacing) * 20);
  }

  .pt-2 {
    padding-top: calc(var(--spacing) * 2);
  }

  .pt-3 {
    padding-top: calc(var(--spacing) * 3);
  }

  .pt-6 {
    padding-top: calc(var(--spacing) * 6);
  }

  .pt-16 {
    padding-top: calc(var(--spacing) * 16);
  }

  .pr-4 {
    padding-right: calc(var(--spacing) * 4);
  }

  .pb-6 {
    padding-bottom: calc(var(--spacing) * 6);
  }

  .pb-20 {
    padding-bottom: calc(var(--spacing) * 20);
  }

  .text-center {
    text-align: center;
  }

  .text-left {
    text-align: left;
  }

  .font-sans {
    font-family: var(--font-sans);
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-3xl {
    font-size: var(--text-3xl);
    line-height: var(--tw-leading, var(--text-3xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-lg {
    font-size: var(--text-lg);
    line-height: var(--tw-leading, var(--text-lg--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .text-xl {
    font-size: var(--text-xl);
    line-height: var(--tw-leading, var(--text-xl--line-height));
  }

  .text-xs {
    font-size: var(--text-xs);
    line-height: var(--tw-leading, var(--text-xs--line-height));
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .leading-tight {
    --tw-leading: var(--leading-tight);
    line-height: var(--leading-tight);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .text-blue-500 {
    color: var(--color-blue-500);
  }

  .text-blue-600 {
    color: var(--color-blue-600);
  }

  .text-blue-800 {
    color: var(--color-blue-800);
  }

  .text-gray-500 {
    color: var(--color-gray-500);
  }

  .text-gray-600 {
    color: var(--color-gray-600);
  }

  .text-gray-900 {
    color: var(--color-gray-900);
  }

  .text-green-500 {
    color: var(--color-green-500);
  }

  .text-green-800 {
    color: var(--color-green-800);
  }

  .text-purple-800 {
    color: var(--color-purple-800);
  }

  .text-red-500 {
    color: var(--color-red-500);
  }

  .text-transparent {
    color: #0000;
  }

  .text-white {
    color: var(--color-white);
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .opacity-5 {
    opacity: .05;
  }

  .shadow {
    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1px 2px -1px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-2xl {
    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #00000040);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .shadow-xl {
    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .blur {
    --tw-blur: blur(8px);
    filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, );
  }

  .backdrop-blur-sm {
    --tw-backdrop-blur: blur(var(--blur-sm));
    backdrop-filter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-backdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rotate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-backdrop-saturate, ) var(--tw-backdrop-sepia, );
  }

  .transition {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-all {
    transition-property: all;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .transition-transform {
    transition-property: transform, translate, scale, rotate;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  .duration-200 {
    --tw-duration: .2s;
    transition-duration: .2s;
  }

  .duration-300 {
    --tw-duration: .3s;
    transition-duration: .3s;
  }

  .ease-out {
    --tw-ease: var(--ease-out);
    transition-timing-function: var(--ease-out);
  }

  @media (hover: hover) {
    .group-hover\:translate-x-1:is(:where(.group):hover *) {
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:scale-105:is(:where(.group):hover *) {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .group-hover\:text-blue-600:is(:where(.group):hover *) {
      color: var(--color-blue-600);
    }
  }

  @media (hover: hover) {
    .group-hover\/btn\:translate-x-1:is(:where(.group\/btn):hover *) {
      --tw-translate-x: calc(var(--spacing) * 1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:-translate-y-1:hover {
      --tw-translate-y: calc(var(--spacing) * -1);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:-translate-y-2:hover {
      --tw-translate-y: calc(var(--spacing) * -2);
      translate: var(--tw-translate-x) var(--tw-translate-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-105:hover {
      --tw-scale-x: 105%;
      --tw-scale-y: 105%;
      --tw-scale-z: 105%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:scale-110:hover {
      --tw-scale-x: 110%;
      --tw-scale-y: 110%;
      --tw-scale-z: 110%;
      scale: var(--tw-scale-x) var(--tw-scale-y);
    }
  }

  @media (hover: hover) {
    .hover\:border-blue-500:hover {
      border-color: var(--color-blue-500);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-600:hover {
      background-color: var(--color-blue-600);
    }
  }

  @media (hover: hover) {
    .hover\:bg-blue-700:hover {
      background-color: var(--color-blue-700);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-50:hover {
      background-color: var(--color-gray-50);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-100:hover {
      background-color: var(--color-gray-100);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-400:hover {
      color: var(--color-blue-400);
    }
  }

  @media (hover: hover) {
    .hover\:text-blue-600:hover {
      color: var(--color-blue-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-600:hover {
      color: var(--color-gray-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-gray-900:hover {
      color: var(--color-gray-900);
    }
  }

  @media (hover: hover) {
    .hover\:text-green-600:hover {
      color: var(--color-green-600);
    }
  }

  @media (hover: hover) {
    .hover\:text-white:hover {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:shadow-lg:hover {
      --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
    }
  }

  .focus\:border-transparent:focus {
    border-color: #0000;
  }

  .focus\:ring-2:focus {
    --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .disabled\:cursor-not-allowed:disabled {
    cursor: not-allowed;
  }

  .disabled\:opacity-50:disabled {
    opacity: .5;
  }

  @media (width >= 40rem) {
    .sm\:inline-flex {
      display: inline-flex;
    }
  }

  @media (width >= 40rem) {
    .sm\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 40rem) {
    .sm\:px-6 {
      padding-inline: calc(var(--spacing) * 6);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:mb-0 {
      margin-bottom: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 48rem) {
    .md\:flex {
      display: flex;
    }
  }

  @media (width >= 48rem) {
    .md\:hidden {
      display: none;
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 48rem) {
    .md\:flex-row {
      flex-direction: row;
    }
  }

  @media (width >= 64rem) {
    .lg\:col-span-1 {
      grid-column: span 1 / span 1;
    }
  }

  @media (width >= 64rem) {
    .lg\:mx-0 {
      margin-inline: calc(var(--spacing) * 0);
    }
  }

  @media (width >= 64rem) {
    .lg\:block {
      display: block;
    }
  }

  @media (width >= 64rem) {
    .lg\:h-\[500px\] {
      height: 500px;
    }
  }

  @media (width >= 64rem) {
    .lg\:h-auto {
      height: auto;
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (width >= 64rem) {
    .lg\:justify-start {
      justify-content: flex-start;
    }
  }

  @media (width >= 64rem) {
    .lg\:gap-16 {
      gap: calc(var(--spacing) * 16);
    }
  }

  @media (width >= 64rem) {
    .lg\:p-12 {
      padding: calc(var(--spacing) * 12);
    }
  }

  @media (width >= 64rem) {
    .lg\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 64rem) {
    .lg\:text-left {
      text-align: left;
    }
  }

  @media (width >= 64rem) {
    .lg\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-blue-900\/30 {
      background-color: #1c398e4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-blue-900\/30 {
        background-color: color-mix(in oklab, var(--color-blue-900) 30%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-green-900\/30 {
      background-color: #0d542b4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-green-900\/30 {
        background-color: color-mix(in oklab, var(--color-green-900) 30%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:bg-purple-900\/30 {
      background-color: #59168b4d;
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:bg-purple-900\/30 {
        background-color: color-mix(in oklab, var(--color-purple-900) 30%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-green-900\/20 {
      --tw-gradient-from: #0d542b33;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-green-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-green-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:from-yellow-900\/20 {
      --tw-gradient-from: #733e0a33;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:from-yellow-900\/20 {
        --tw-gradient-from: color-mix(in oklab, var(--color-yellow-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-emerald-900\/20 {
      --tw-gradient-to: #004e3b33;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-emerald-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-emerald-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:to-orange-900\/20 {
      --tw-gradient-to: #7e2a0c33;
      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));
    }

    @supports (color: color-mix(in lab, red, red)) {
      .dark\:to-orange-900\/20 {
        --tw-gradient-to: color-mix(in oklab, var(--color-orange-900) 20%, transparent);
      }
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-blue-300 {
      color: var(--color-blue-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-green-300 {
      color: var(--color-green-300);
    }
  }

  @media (prefers-color-scheme: dark) {
    .dark\:text-purple-300 {
      color: var(--color-purple-300);
    }
  }

  .text-balance {
    text-wrap: balance;
  }

  .gradient-text {
    background: linear-gradient(135deg, var(--color-h1), var(--color-h1-end));
    -webkit-text-fill-color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
  }

  .line-clamp-2 {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-3 {
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }

  .line-clamp-4 {
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
  }
}

:root {
  --primary-blue: #2563eb;
  --primary-cyan: #06b6d4;
  --accent-purple: #8b5cf6;
  --accent-pink: #ec4899;
  --text-dark: #1f2937;
  --text-gray: #6b7280;
  --text-light: #9ca3af;
  --bg-white: #fff;
  --bg-gray-50: #f9fafb;
  --bg-gray-100: #f3f4f6;
  --bg-gray-900: #111827;
  --border-gray: #e5e7eb;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  color: var(--text-dark);
  background: var(--bg-white);
  font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, sans-serif;
  line-height: 1.6;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-blue);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-cyan);
}

@property --tw-translate-x {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-y {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-translate-z {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-rotate-x {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-y {
  syntax: "*";
  inherits: false
}

@property --tw-rotate-z {
  syntax: "*";
  inherits: false
}

@property --tw-skew-x {
  syntax: "*";
  inherits: false
}

@property --tw-skew-y {
  syntax: "*";
  inherits: false
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-space-x-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-gradient-position {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-via {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-to {
  syntax: "<color>";
  inherits: false;
  initial-value: #0000;
}

@property --tw-gradient-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-via-stops {
  syntax: "*";
  inherits: false
}

@property --tw-gradient-from-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 0%;
}

@property --tw-gradient-via-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 50%;
}

@property --tw-gradient-to-position {
  syntax: "<length-percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-blur {
  syntax: "*";
  inherits: false
}

@property --tw-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-invert {
  syntax: "*";
  inherits: false
}

@property --tw-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-drop-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-drop-shadow-size {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-blur {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-brightness {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-contrast {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-grayscale {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-hue-rotate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-invert {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-opacity {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-saturate {
  syntax: "*";
  inherits: false
}

@property --tw-backdrop-sepia {
  syntax: "*";
  inherits: false
}

@property --tw-duration {
  syntax: "*";
  inherits: false
}

@property --tw-ease {
  syntax: "*";
  inherits: false
}

@property --tw-scale-x {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-y {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@property --tw-scale-z {
  syntax: "*";
  inherits: false;
  initial-value: 1;
}

@keyframes bounce {
  0%, 100% {
    animation-timing-function: cubic-bezier(.8, 0, 1, 1);
    transform: translateY(-25%);
  }

  50% {
    animation-timing-function: cubic-bezier(0, 0, .2, 1);
    transform: none;
  }
}


/*# sourceMappingURL=%5Broot-of-the-server%5D__404369a3._.css.map*/