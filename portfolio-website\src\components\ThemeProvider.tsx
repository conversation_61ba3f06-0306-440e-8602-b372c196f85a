'use client';

import { ThemeProvider as NextThemesProvider } from 'next-themes';
import { type ThemeProviderProps } from 'next-themes/dist/types';
import { useEffect, useState } from 'react';

interface ThemeSelectionModalProps {
  isOpen: boolean;
  onSelect: (theme: 'light' | 'dark') => void;
}

function ThemeSelectionModal({ isOpen, onSelect }: ThemeSelectionModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className="bg-white dark:bg-dark-card rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-dark-h2 mb-2">
            Choose Your Theme
          </h2>
          <p className="text-gray-600 dark:text-dark-secondary">
            Select your preferred viewing experience
          </p>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <button
            onClick={() => onSelect('light')}
            className="group p-6 rounded-xl border-2 border-gray-200 hover:border-light-primary transition-all duration-300 hover:shadow-lg"
          >
            <div className="w-12 h-12 mx-auto mb-3 rounded-lg bg-gradient-to-br from-blue-50 to-cyan-50 flex items-center justify-center">
              <svg className="w-6 h-6 text-light-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1">Light Mode</h3>
            <p className="text-sm text-gray-600">Clean and bright interface</p>
          </button>
          
          <button
            onClick={() => onSelect('dark')}
            className="group p-6 rounded-xl border-2 border-gray-200 hover:border-dark-primary transition-all duration-300 hover:shadow-lg"
          >
            <div className="w-12 h-12 mx-auto mb-3 rounded-lg bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
              <svg className="w-6 h-6 text-dark-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 mb-1">Dark Mode</h3>
            <p className="text-sm text-gray-600">Easy on the eyes</p>
          </button>
        </div>
      </div>
    </div>
  );
}

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  const [showModal, setShowModal] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    
    // Check if user has visited before
    const hasVisited = localStorage.getItem('theme-selected');
    if (!hasVisited) {
      setShowModal(true);
    }
  }, []);

  const handleThemeSelect = (theme: 'light' | 'dark') => {
    localStorage.setItem('theme-selected', 'true');
    localStorage.setItem('theme', theme);
    setShowModal(false);
    
    // Apply theme immediately
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  if (!mounted) {
    return null;
  }

  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange={false}
      {...props}
    >
      {children}
      <ThemeSelectionModal isOpen={showModal} onSelect={handleThemeSelect} />
    </NextThemesProvider>
  );
}
