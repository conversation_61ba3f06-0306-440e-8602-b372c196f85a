{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date: string | Date): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-');\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength).trim() + '...';\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function getRandomItems<T>(array: T[], count: number): T[] {\n  const shuffled = [...array].sort(() => 0.5 - Math.random());\n  return shuffled.slice(0, count);\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,IAAI,KAAK;AAC3C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,eAAkB,KAAU,EAAE,KAAa;IACzD,MAAM,WAAW;WAAI;KAAM,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IACxD,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useTheme } from 'next-themes';\nimport { Moon, Sun, Menu, X } from 'lucide-react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { cn } from '@/lib/utils';\nimport type { NavigationItem } from '@/types';\n\ninterface HeaderProps {\n  onContactClick: () => void;\n}\n\nconst navigationItems: NavigationItem[] = [\n  { id: 'home', label: 'Home', href: '#home' },\n  { id: 'services', label: 'Services', href: '#services' },\n  { id: 'portfolio', label: 'Portfolio', href: '#portfolio' },\n  { id: 'reviews', label: 'Reviews', href: '#reviews' },\n  { id: 'faq', label: 'FAQ', href: '#faq' },\n];\n\nexport function Header({ onContactClick }: HeaderProps) {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [activeSection, setActiveSection] = useState('home');\n  const { theme, setTheme } = useTheme();\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n\n    const handleSectionChange = () => {\n      const sections = navigationItems.map(item => item.id);\n      const currentSection = sections.find(section => {\n        const element = document.getElementById(section);\n        if (element) {\n          const rect = element.getBoundingClientRect();\n          return rect.top <= 100 && rect.bottom >= 100;\n        }\n        return false;\n      });\n      \n      if (currentSection) {\n        setActiveSection(currentSection);\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    window.addEventListener('scroll', handleSectionChange);\n    \n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n      window.removeEventListener('scroll', handleSectionChange);\n    };\n  }, []);\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n      setIsMobileMenuOpen(false);\n    }\n  };\n\n  const toggleTheme = () => {\n    setTheme(theme === 'dark' ? 'light' : 'dark');\n  };\n\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <motion.header\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      className={cn(\n        'fixed top-0 left-0 right-0 z-40 transition-all duration-300',\n        isScrolled\n          ? 'bg-white/80 dark:bg-dark-background/80 backdrop-blur-md shadow-lg'\n          : 'bg-transparent'\n      )}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo */}\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: 0.1 }}\n            className=\"flex-shrink-0\"\n          >\n            <a\n              href=\"#home\"\n              onClick={(e) => {\n                e.preventDefault();\n                scrollToSection('#home');\n              }}\n              className=\"text-2xl lg:text-3xl font-bold gradient-text hover:scale-105 transition-transform duration-300\"\n            >\n              DENIS\n            </a>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            {navigationItems.map((item, index) => (\n              <motion.a\n                key={item.id}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.1 + index * 0.1 }}\n                href={item.href}\n                onClick={(e) => {\n                  e.preventDefault();\n                  scrollToSection(item.href);\n                }}\n                className={cn(\n                  'relative px-3 py-2 text-sm font-medium transition-colors duration-300',\n                  activeSection === item.id\n                    ? 'text-dark-primary dark:text-dark-primary'\n                    : 'text-light-body dark:text-dark-body hover:text-light-primary dark:hover:text-dark-primary'\n                )}\n              >\n                {item.label}\n                {activeSection === item.id && (\n                  <motion.div\n                    layoutId=\"activeSection\"\n                    className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-primary\"\n                    initial={false}\n                    transition={{ type: \"spring\", stiffness: 380, damping: 30 }}\n                  />\n                )}\n              </motion.a>\n            ))}\n          </nav>\n\n          {/* Right Side Actions */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Theme Toggle */}\n            <motion.button\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.3 }}\n              onClick={toggleTheme}\n              className=\"p-2 rounded-lg bg-light-card dark:bg-dark-card hover:bg-light-primary/10 dark:hover:bg-dark-primary/10 transition-colors duration-300\"\n              aria-label=\"Toggle theme\"\n            >\n              {theme === 'dark' ? (\n                <Sun className=\"w-5 h-5 text-dark-primary\" />\n              ) : (\n                <Moon className=\"w-5 h-5 text-light-primary\" />\n              )}\n            </motion.button>\n\n            {/* Get In Touch Button */}\n            <motion.button\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.4 }}\n              onClick={onContactClick}\n              className=\"hidden sm:inline-flex items-center px-6 py-2.5 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-medium rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105\"\n            >\n              Get In Touch\n            </motion.button>\n\n            {/* Mobile Menu Button */}\n            <motion.button\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.5 }}\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"lg:hidden p-2 rounded-lg bg-light-card dark:bg-dark-card hover:bg-light-primary/10 dark:hover:bg-dark-primary/10 transition-colors duration-300\"\n              aria-label=\"Toggle mobile menu\"\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"w-5 h-5 text-light-body dark:text-dark-body\" />\n              ) : (\n                <Menu className=\"w-5 h-5 text-light-body dark:text-dark-body\" />\n              )}\n            </motion.button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"lg:hidden bg-white/95 dark:bg-dark-background/95 backdrop-blur-md border-t border-light-primary/20 dark:border-dark-primary/20\"\n          >\n            <div className=\"max-w-7xl mx-auto px-4 py-4\">\n              <nav className=\"flex flex-col space-y-2\">\n                {navigationItems.map((item) => (\n                  <a\n                    key={item.id}\n                    href={item.href}\n                    onClick={(e) => {\n                      e.preventDefault();\n                      scrollToSection(item.href);\n                    }}\n                    className={cn(\n                      'px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-300',\n                      activeSection === item.id\n                        ? 'bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary'\n                        : 'text-light-body dark:text-dark-body hover:bg-light-primary/5 dark:hover:bg-dark-primary/5'\n                    )}\n                  >\n                    {item.label}\n                  </a>\n                ))}\n                <button\n                  onClick={() => {\n                    onContactClick();\n                    setIsMobileMenuOpen(false);\n                  }}\n                  className=\"mt-4 w-full px-4 py-3 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-medium rounded-lg hover:shadow-neon transition-all duration-300\"\n                >\n                  Get In Touch\n                </button>\n              </nav>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AANA;;;;;;;AAaA,MAAM,kBAAoC;IACxC;QAAE,IAAI;QAAQ,OAAO;QAAQ,MAAM;IAAQ;IAC3C;QAAE,IAAI;QAAY,OAAO;QAAY,MAAM;IAAY;IACvD;QAAE,IAAI;QAAa,OAAO;QAAa,MAAM;IAAa;IAC1D;QAAE,IAAI;QAAW,OAAO;QAAW,MAAM;IAAW;IACpD;QAAE,IAAI;QAAO,OAAO;QAAO,MAAM;IAAO;CACzC;AAEM,SAAS,OAAO,EAAE,cAAc,EAAe;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,MAAM,sBAAsB;YAC1B,MAAM,WAAW,gBAAgB,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;YACpD,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAA;gBACnC,MAAM,UAAU,SAAS,cAAc,CAAC;gBACxC,IAAI,SAAS;oBACX,MAAM,OAAO,QAAQ,qBAAqB;oBAC1C,OAAO,KAAK,GAAG,IAAI,OAAO,KAAK,MAAM,IAAI;gBAC3C;gBACA,OAAO;YACT;YAEA,IAAI,gBAAgB;gBAClB,iBAAiB;YACnB;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;YAC5C,oBAAoB;QACtB;IACF;IAEA,MAAM,cAAc;QAClB,SAAS,UAAU,SAAS,UAAU;IACxC;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,sEACA;;0BAGN,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCAEV,cAAA,8OAAC;gCACC,MAAK;gCACL,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,gBAAgB;gCAClB;gCACA,WAAU;0CACX;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,MAAM,QAAQ;oCAAI;oCACvC,MAAM,KAAK,IAAI;oCACf,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,gBAAgB,KAAK,IAAI;oCAC3B;oCACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yEACA,kBAAkB,KAAK,EAAE,GACrB,6CACA;;wCAGL,KAAK,KAAK;wCACV,kBAAkB,KAAK,EAAE,kBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,UAAS;4CACT,WAAU;4CACV,SAAS;4CACT,YAAY;gDAAE,MAAM;gDAAU,WAAW;gDAAK,SAAS;4CAAG;;;;;;;mCAtBzD,KAAK,EAAE;;;;;;;;;;sCA8BlB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;oCAAI;oCACzB,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEV,UAAU,uBACT,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;6DAEf,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAKpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;oCAAI;oCACzB,SAAS;oCACT,WAAU;8CACX;;;;;;8CAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;oCAAI;oCACzB,SAAS,IAAM,oBAAoB,CAAC;oCACpC,WAAU;oCACV,cAAW;8CAEV,iCACC,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAEb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,8OAAC,yLAAA,CAAA,kBAAe;0BACb,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,gBAAgB,KAAK,IAAI;wCAC3B;wCACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2EACA,kBAAkB,KAAK,EAAE,GACrB,0FACA;kDAGL,KAAK,KAAK;uCAbN,KAAK,EAAE;;;;;8CAgBhB,8OAAC;oCACC,SAAS;wCACP;wCACA,oBAAoB;oCACtB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { ArrowRight, Play, Star, Users, TrendingUp } from 'lucide-react';\n\ninterface HeroProps {\n  onContactClick: () => void;\n}\n\nconst platformIcons = [\n  { name: 'WordPress', icon: '🔧' },\n  { name: 'Google Analytics', icon: '📊' },\n  { name: 'OpenAI', icon: '🤖' },\n  { name: 'Automation', icon: '⚡' },\n  { name: 'Data Science', icon: '📈' },\n];\n\nconst stats = [\n  { label: 'Projects Completed', value: '150+', icon: TrendingUp },\n  { label: 'Happy Clients', value: '50+', icon: Users },\n  { label: 'Success Rate', value: '98%', icon: Star },\n];\n\nexport function Hero({ onContactClick }: HeroProps) {\n  const scrollToPortfolio = () => {\n    const element = document.querySelector('#portfolio');\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section id=\"home\" className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-light-background dark:bg-dark-background\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-light-primary/5 via-transparent to-light-primary/10 dark:from-dark-primary/5 dark:to-dark-primary/10\" />\n      \n      {/* Floating Elements */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(6)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-light-primary/20 dark:bg-dark-primary/20 rounded-full\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, 50, 0],\n              opacity: [0.3, 0.8, 0.3],\n            }}\n            transition={{\n              duration: 6 + i,\n              repeat: Infinity,\n              delay: i * 0.5,\n            }}\n            style={{\n              left: `${10 + i * 15}%`,\n              top: `${20 + i * 10}%`,\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16\">\n        <div className=\"grid lg:grid-cols-2 gap-12 lg:gap-16 items-center\">\n          {/* Left Content */}\n          <div className=\"text-center lg:text-left\">\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8 }}\n              className=\"mb-6\"\n            >\n              <span className=\"inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4\">\n                🚀 AI & Automation Specialist\n              </span>\n              \n              <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6\">\n                <span className=\"gradient-text\">Transform Your Business</span>\n                <br />\n                <span className=\"text-light-h2 dark:text-dark-h2\">\n                  with Intelligent\n                </span>\n                <br />\n                <span className=\"gradient-text\">Automation</span>\n              </h1>\n              \n              <p className=\"text-lg sm:text-xl text-light-secondary dark:text-dark-secondary max-w-2xl mx-auto lg:mx-0 leading-relaxed\">\n                From Kenya to global success, I help businesses unlock their potential through \n                cutting-edge AI solutions and automation strategies that deliver measurable results.\n              </p>\n            </motion.div>\n\n            {/* Stats */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"grid grid-cols-3 gap-4 mb-8\"\n            >\n              {stats.map((stat, index) => (\n                <div key={stat.label} className=\"text-center\">\n                  <div className=\"flex items-center justify-center mb-2\">\n                    <stat.icon className=\"w-5 h-5 text-light-primary dark:text-dark-primary mr-2\" />\n                    <span className=\"text-2xl font-bold gradient-text\">{stat.value}</span>\n                  </div>\n                  <p className=\"text-sm text-light-secondary dark:text-dark-secondary\">{stat.label}</p>\n                </div>\n              ))}\n            </motion.div>\n\n            {/* CTAs */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\"\n            >\n              <button\n                onClick={scrollToPortfolio}\n                className=\"group inline-flex items-center px-8 py-4 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-semibold rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105\"\n              >\n                View Case Studies\n                <ArrowRight className=\"ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n              </button>\n              \n              <button\n                onClick={onContactClick}\n                className=\"group inline-flex items-center px-8 py-4 border-2 border-light-primary dark:border-dark-primary text-light-primary dark:text-dark-primary font-semibold rounded-lg hover:bg-light-primary dark:hover:bg-dark-primary hover:text-white transition-all duration-300\"\n              >\n                <Play className=\"mr-2 w-5 h-5\" />\n                Book a Call\n              </button>\n            </motion.div>\n          </div>\n\n          {/* Right Content - Image & Platform Icons */}\n          <div className=\"relative\">\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.8, delay: 0.3 }}\n              className=\"relative\"\n            >\n              {/* Main Image */}\n              <div className=\"relative w-80 h-80 mx-auto lg:w-96 lg:h-96\">\n                <div className=\"absolute inset-0 bg-gradient-primary rounded-full opacity-20 animate-pulse\" />\n                <div className=\"relative w-full h-full rounded-full overflow-hidden border-4 border-light-primary dark:border-dark-primary shadow-2xl\">\n                  <Image\n                    src=\"/Denis-Personal-picture.png\"\n                    alt=\"Denis - AI & Automation Specialist\"\n                    fill\n                    className=\"object-cover\"\n                    priority\n                  />\n                </div>\n              </div>\n\n              {/* Floating Platform Icons */}\n              {platformIcons.map((platform, index) => (\n                <motion.div\n                  key={platform.name}\n                  initial={{ opacity: 0, scale: 0 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ \n                    duration: 0.5, \n                    delay: 0.6 + index * 0.1,\n                    type: \"spring\",\n                    stiffness: 200 \n                  }}\n                  className=\"absolute w-16 h-16 bg-white dark:bg-dark-card rounded-full shadow-lg flex items-center justify-center text-2xl hover:scale-110 transition-transform duration-300 cursor-pointer\"\n                  style={{\n                    top: `${15 + Math.sin(index * 1.2) * 30}%`,\n                    left: `${10 + Math.cos(index * 1.2) * 35}%`,\n                    transform: index % 2 === 0 ? 'translateX(100%)' : 'translateX(-50%)',\n                  }}\n                  animate={{\n                    y: [0, -10, 0],\n                  }}\n                  transition={{\n                    duration: 3 + index * 0.5,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                  }}\n                  title={platform.name}\n                >\n                  {platform.icon}\n                </motion.div>\n              ))}\n            </motion.div>\n\n            {/* Experience Badge */}\n            <motion.div\n              initial={{ opacity: 0, x: 50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8, delay: 0.8 }}\n              className=\"absolute -bottom-6 -right-6 bg-white dark:bg-dark-card rounded-2xl p-4 shadow-xl border border-light-primary/20 dark:border-dark-primary/20\"\n            >\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold gradient-text\">5+</div>\n                <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Years Experience</div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.8, delay: 1 }}\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n        >\n          <motion.div\n            animate={{ y: [0, 10, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-6 h-10 border-2 border-light-primary dark:border-dark-primary rounded-full flex justify-center\"\n          >\n            <motion.div\n              animate={{ y: [0, 12, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n              className=\"w-1 h-3 bg-light-primary dark:bg-dark-primary rounded-full mt-2\"\n            />\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAUA,MAAM,gBAAgB;IACpB;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAI;IAChC;QAAE,MAAM;QAAgB,MAAM;IAAK;CACpC;AAED,MAAM,QAAQ;IACZ;QAAE,OAAO;QAAsB,OAAO;QAAQ,MAAM,kNAAA,CAAA,aAAU;IAAC;IAC/D;QAAE,OAAO;QAAiB,OAAO;QAAO,MAAM,oMAAA,CAAA,QAAK;IAAC;IACpD;QAAE,OAAO;QAAgB,OAAO;QAAO,MAAM,kMAAA,CAAA,OAAI;IAAC;CACnD;AAEM,SAAS,KAAK,EAAE,cAAc,EAAa;IAChD,MAAM,oBAAoB;QACxB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;;0BAE3B,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAK;6BAAE;4BACf,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;4BACb,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;wBAC1B;wBACA,YAAY;4BACV,UAAU,IAAI;4BACd,QAAQ;4BACR,OAAO,IAAI;wBACb;wBACA,OAAO;4BACL,MAAM,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;4BACvB,KAAK,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;wBACxB;uBAfK;;;;;;;;;;0BAoBX,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAqJ;;;;;;0DAIrK,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;;;;;kEACD,8OAAC;wDAAK,WAAU;kEAAkC;;;;;;kEAGlD,8OAAC;;;;;kEACD,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAGlC,8OAAC;gDAAE,WAAU;0DAA6G;;;;;;;;;;;;kDAO5H,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gDAAqB,WAAU;;kEAC9B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;0EACrB,8OAAC;gEAAK,WAAU;0EAAoC,KAAK,KAAK;;;;;;;;;;;;kEAEhE,8OAAC;wDAAE,WAAU;kEAAyD,KAAK,KAAK;;;;;;;+CALxE,KAAK,KAAK;;;;;;;;;;kDAWxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,8OAAC;gDACC,SAAS;gDACT,WAAU;;oDACX;kEAEC,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;0DAGxB,8OAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAOvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAGV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,IAAI;4DACJ,WAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;4CAMb,cAAc,GAAG,CAAC,CAAC,UAAU,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,YAAY;wDACV,UAAU;wDACV,OAAO,MAAM,QAAQ;wDACrB,MAAM;wDACN,WAAW;oDACb;oDACA,WAAU;oDACV,OAAO;wDACL,KAAK,GAAG,KAAK,KAAK,GAAG,CAAC,QAAQ,OAAO,GAAG,CAAC,CAAC;wDAC1C,MAAM,GAAG,KAAK,KAAK,GAAG,CAAC,QAAQ,OAAO,GAAG,CAAC,CAAC;wDAC3C,WAAW,QAAQ,MAAM,IAAI,qBAAqB;oDACpD;oDACA,SAAS;wDACP,GAAG;4DAAC;4DAAG,CAAC;4DAAI;yDAAE;oDAChB;oDACA,YAAY;wDACV,UAAU,IAAI,QAAQ;wDACtB,QAAQ;wDACR,MAAM;oDACR;oDACA,OAAO,SAAS,IAAI;8DAEnB,SAAS,IAAI;mDAzBT,SAAS,IAAI;;;;;;;;;;;kDA+BxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,8OAAC;oDAAI,WAAU;8DAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAE;wBACtC,WAAU;kCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;oCAAC;oCAAG;oCAAI;iCAAE;4BAAC;4BACzB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;4BAC5C,WAAU;sCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;gCAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB", "debugId": null}}, {"offset": {"line": 1006, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/ContactModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X, Mail, Calendar, MessageSquare, Send, Phone } from 'lucide-react';\nimport { useForm } from 'react-hook-form';\nimport toast from 'react-hot-toast';\nimport { cn } from '@/lib/utils';\n\ninterface ContactModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\ninterface ContactFormData {\n  name: string;\n  email: string;\n  subject: string;\n  message: string;\n}\n\nexport function ContactModal({ isOpen, onClose }: ContactModalProps) {\n  const [activeTab, setActiveTab] = useState<'options' | 'form'>('options');\n  const { register, handleSubmit, formState: { errors }, reset } = useForm<ContactFormData>();\n\n  const handleEmailClick = () => {\n    window.open('mailto:<EMAIL>?subject=Let\\'s Connect!', '_blank');\n    onClose();\n  };\n\n  const handleCallClick = () => {\n    window.open('https://calendly.com/denis-ai-specialist/30min', '_blank');\n    onClose();\n  };\n\n  const handleFormSubmit = async (data: ContactFormData) => {\n    try {\n      // Simulate form submission\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      toast.success('Message sent successfully! I\\'ll get back to you soon.');\n      reset();\n      onClose();\n    } catch (error) {\n      toast.error('Failed to send message. Please try again.');\n    }\n  };\n\n  const contactOptions = [\n    {\n      id: 'email',\n      title: 'Send an Email',\n      description: 'Quick and direct communication',\n      icon: Mail,\n      action: handleEmailClick,\n      color: 'from-blue-500 to-cyan-500',\n    },\n    {\n      id: 'call',\n      title: 'Book a 30 Min Call',\n      description: 'Let\\'s discuss your project in detail',\n      icon: Calendar,\n      action: handleCallClick,\n      color: 'from-green-500 to-emerald-500',\n    },\n    {\n      id: 'form',\n      title: 'Contact Form',\n      description: 'Tell me about your needs',\n      icon: MessageSquare,\n      action: () => setActiveTab('form'),\n      color: 'from-purple-500 to-pink-500',\n    },\n  ];\n\n  if (!isOpen) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4\"\n        onClick={onClose}\n      >\n        <motion.div\n          initial={{ scale: 0.9, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          exit={{ scale: 0.9, opacity: 0 }}\n          transition={{ type: \"spring\", duration: 0.5 }}\n          className=\"bg-white dark:bg-dark-card rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden\"\n          onClick={(e) => e.stopPropagation()}\n        >\n          {/* Header */}\n          <div className=\"relative p-6 border-b border-gray-200 dark:border-gray-700\">\n            <button\n              onClick={onClose}\n              className=\"absolute top-4 right-4 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n            >\n              <X className=\"w-5 h-5 text-gray-500 dark:text-gray-400\" />\n            </button>\n            \n            <div className=\"text-center\">\n              <h2 className=\"text-2xl font-bold text-light-h2 dark:text-dark-h2 mb-2\">\n                We'd Love to Hear From You!\n              </h2>\n              <p className=\"text-light-secondary dark:text-dark-secondary\">\n                {activeTab === 'options' \n                  ? \"Choose how you'd like to connect:\" \n                  : \"Tell us about your project\"\n                }\n              </p>\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-6\">\n            {activeTab === 'options' ? (\n              <div className=\"space-y-4\">\n                {contactOptions.map((option, index) => (\n                  <motion.button\n                    key={option.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    onClick={option.action}\n                    className=\"w-full p-4 rounded-xl border-2 border-gray-200 dark:border-gray-700 hover:border-light-primary dark:hover:border-dark-primary transition-all duration-300 hover:shadow-lg group\"\n                  >\n                    <div className=\"flex items-center space-x-4\">\n                      <div className={cn(\n                        \"w-12 h-12 rounded-lg bg-gradient-to-br flex items-center justify-center\",\n                        option.color\n                      )}>\n                        <option.icon className=\"w-6 h-6 text-white\" />\n                      </div>\n                      <div className=\"flex-1 text-left\">\n                        <h3 className=\"font-semibold text-light-h3 dark:text-dark-h3 group-hover:text-light-primary dark:group-hover:text-dark-primary transition-colors\">\n                          {option.title}\n                        </h3>\n                        <p className=\"text-sm text-light-secondary dark:text-dark-secondary\">\n                          {option.description}\n                        </p>\n                      </div>\n                    </div>\n                  </motion.button>\n                ))}\n              </div>\n            ) : (\n              <motion.form\n                initial={{ opacity: 0, x: 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                onSubmit={handleSubmit(handleFormSubmit)}\n                className=\"space-y-4\"\n              >\n                <button\n                  type=\"button\"\n                  onClick={() => setActiveTab('options')}\n                  className=\"flex items-center text-sm text-light-primary dark:text-dark-primary hover:underline mb-4\"\n                >\n                  ← Back to options\n                </button>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-light-body dark:text-dark-body mb-2\">\n                    Name *\n                  </label>\n                  <input\n                    {...register('name', { required: 'Name is required' })}\n                    className=\"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors\"\n                    placeholder=\"Your full name\"\n                  />\n                  {errors.name && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.name.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-light-body dark:text-dark-body mb-2\">\n                    Email *\n                  </label>\n                  <input\n                    {...register('email', { \n                      required: 'Email is required',\n                      pattern: {\n                        value: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n                        message: 'Please enter a valid email'\n                      }\n                    })}\n                    type=\"email\"\n                    className=\"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                  {errors.email && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.email.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-light-body dark:text-dark-body mb-2\">\n                    Subject *\n                  </label>\n                  <input\n                    {...register('subject', { required: 'Subject is required' })}\n                    className=\"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors\"\n                    placeholder=\"What's this about?\"\n                  />\n                  {errors.subject && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.subject.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-light-body dark:text-dark-body mb-2\">\n                    Message *\n                  </label>\n                  <textarea\n                    {...register('message', { required: 'Message is required' })}\n                    rows={4}\n                    className=\"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors resize-none\"\n                    placeholder=\"Tell me about your project or how I can help...\"\n                  />\n                  {errors.message && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.message.message}</p>\n                  )}\n                </div>\n\n                <button\n                  type=\"submit\"\n                  className=\"w-full flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-medium rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105\"\n                >\n                  <Send className=\"w-4 h-4\" />\n                  <span>Send Message</span>\n                </button>\n              </motion.form>\n            )}\n          </div>\n        </motion.div>\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AAqBO,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAqB;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/D,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IAEvE,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC,oDAAoD;QAChE;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC,kDAAkD;QAC9D;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,2BAA2B;YAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA;QACF,EAAE,OAAO,OAAO;YACd,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,0MAAA,CAAA,WAAQ;YACd,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,wNAAA,CAAA,gBAAa;YACnB,QAAQ,IAAM,aAAa;YAC3B,OAAO;QACT;KACD;IAED,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;sBAET,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAC/B,YAAY;oBAAE,MAAM;oBAAU,UAAU;gBAAI;gBAC5C,WAAU;gBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAGjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0D;;;;;;kDAGxE,8OAAC;wCAAE,WAAU;kDACV,cAAc,YACX,sCACA;;;;;;;;;;;;;;;;;;kCAOV,8OAAC;wBAAI,WAAU;kCACZ,cAAc,0BACb,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;oCACjC,SAAS,OAAO,MAAM;oCACtB,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,2EACA,OAAO,KAAK;0DAEZ,cAAA,8OAAC,OAAO,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,OAAO,KAAK;;;;;;kEAEf,8OAAC;wDAAE,WAAU;kEACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;mCAnBpB,OAAO,EAAE;;;;;;;;;iDA2BpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,UAAU,aAAa;4BACvB,WAAU;;8CAEV,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CACX;;;;;;8CAID,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAqE;;;;;;sDAGtF,8OAAC;4CACE,GAAG,SAAS,QAAQ;gDAAE,UAAU;4CAAmB,EAAE;4CACtD,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,IAAI,kBACV,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;8CAIjE,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAqE;;;;;;sDAGtF,8OAAC;4CACE,GAAG,SAAS,SAAS;gDACpB,UAAU;gDACV,SAAS;oDACP,OAAO;oDACP,SAAS;gDACX;4CACF,EAAE;4CACF,MAAK;4CACL,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,KAAK,kBACX,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAIlE,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAqE;;;;;;sDAGtF,8OAAC;4CACE,GAAG,SAAS,WAAW;gDAAE,UAAU;4CAAsB,EAAE;4CAC5D,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,OAAO,kBACb,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;8CAIpE,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAqE;;;;;;sDAGtF,8OAAC;4CACE,GAAG,SAAS,WAAW;gDAAE,UAAU;4CAAsB,EAAE;4CAC5D,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,OAAO,kBACb,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;8CAIpE,8OAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB", "debugId": null}}, {"offset": {"line": 1461, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Header } from '@/components/Header';\nimport { Hero } from '@/components/Hero';\nimport { BlogSection } from '@/components/BlogSection';\nimport { PersonalStory } from '@/components/PersonalStory';\nimport { TestimonialWall } from '@/components/TestimonialWall';\nimport { ContactModal } from '@/components/ContactModal';\n\nexport default function Home() {\n  const [isContactModalOpen, setIsContactModalOpen] = useState(false);\n\n  const handleContactClick = () => {\n    setIsContactModalOpen(true);\n  };\n\n  const handleContactClose = () => {\n    setIsContactModalOpen(false);\n  };\n\n  return (\n    <main className=\"min-h-screen bg-light-background dark:bg-dark-background text-light-body dark:text-dark-body\">\n      <Header onContactClick={handleContactClick} />\n\n      <Hero onContactClick={handleContactClick} />\n\n      {/* Placeholder sections for now */}\n      <section id=\"services\" className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl font-bold gradient-text mb-4\">Services</h2>\n          <p className=\"text-light-secondary dark:text-dark-secondary\">Coming soon...</p>\n        </div>\n      </section>\n\n      <section id=\"portfolio\" className=\"min-h-screen flex items-center justify-center bg-light-card dark:bg-dark-card\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl font-bold gradient-text mb-4\">Portfolio</h2>\n          <p className=\"text-light-secondary dark:text-dark-secondary\">Coming soon...</p>\n        </div>\n      </section>\n\n      <section id=\"reviews\" className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl font-bold gradient-text mb-4\">Reviews</h2>\n          <p className=\"text-light-secondary dark:text-dark-secondary\">Coming soon...</p>\n        </div>\n      </section>\n\n      <section id=\"faq\" className=\"min-h-screen flex items-center justify-center bg-light-card dark:bg-dark-card\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl font-bold gradient-text mb-4\">FAQ</h2>\n          <p className=\"text-light-secondary dark:text-dark-secondary\">Coming soon...</p>\n        </div>\n      </section>\n\n      <ContactModal\n        isOpen={isContactModalOpen}\n        onClose={handleContactClose}\n      />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAIA;AARA;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,qBAAqB;QACzB,sBAAsB;IACxB;IAEA,MAAM,qBAAqB;QACzB,sBAAsB;IACxB;IAEA,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC,4HAAA,CAAA,SAAM;gBAAC,gBAAgB;;;;;;0BAExB,8OAAC,0HAAA,CAAA,OAAI;gBAAC,gBAAgB;;;;;;0BAGtB,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;;;;;;0BAIjE,8OAAC;gBAAQ,IAAG;gBAAY,WAAU;0BAChC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;;;;;;0BAIjE,8OAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;;;;;;0BAIjE,8OAAC;gBAAQ,IAAG;gBAAM,WAAU;0BAC1B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;;;;;;0BAIjE,8OAAC,kIAAA,CAAA,eAAY;gBACX,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}