{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\nimport { ArrowRight, Play } from 'lucide-react';\n\ninterface HeroProps {\n  onContactClick: () => void;\n}\n\nexport function Hero({ onContactClick }: HeroProps) {\n  const scrollToPortfolio = () => {\n    const element = document.querySelector('#portfolio');\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section id=\"home\" className=\"pt-16 pb-20 bg-gradient-to-br from-blue-50 via-white to-cyan-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid lg:grid-cols-2 gap-12 lg:gap-16 items-center min-h-[80vh]\">\n          {/* Left Content */}\n          <div className=\"text-center lg:text-left\">\n            <div className=\"mb-8\">\n              <div className=\"inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6\">\n                <span className=\"w-2 h-2 bg-blue-600 rounded-full mr-2\"></span>\n                AI & Automation Specialist\n              </div>\n\n              <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6\">\n                Transform Your Business with\n                <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600\"> AI-Powered </span>\n                Solutions\n              </h1>\n\n              <p className=\"text-xl text-gray-600 max-w-2xl mx-auto lg:mx-0 leading-relaxed mb-8\">\n                From Kenya to global success, I help businesses unlock their potential through\n                cutting-edge AI solutions and automation strategies that deliver measurable results.\n              </p>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-3 gap-8 mb-10\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-gray-900 mb-1\">150+</div>\n                <p className=\"text-sm text-gray-600\">Projects Completed</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-gray-900 mb-1\">50+</div>\n                <p className=\"text-sm text-gray-600\">Happy Clients</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-gray-900 mb-1\">98%</div>\n                <p className=\"text-sm text-gray-600\">Success Rate</p>\n              </div>\n            </div>\n\n            {/* CTAs */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\">\n              <button\n                onClick={scrollToPortfolio}\n                className=\"inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200\"\n              >\n                View Case Studies\n                <ArrowRight className=\"ml-2 w-5 h-5\" />\n              </button>\n\n              <button\n                onClick={onContactClick}\n                className=\"inline-flex items-center px-8 py-4 border-2 border-blue-600 text-blue-600 font-semibold rounded-lg hover:bg-blue-600 hover:text-white transition-all duration-200\"\n              >\n                <Play className=\"mr-2 w-5 h-5\" />\n                Book a Call\n              </button>\n            </div>\n          </div>\n\n          {/* Right Content - Professional Image */}\n          <div className=\"relative\">\n            <div className=\"relative w-full max-w-lg mx-auto\">\n              {/* Main Image */}\n              <div className=\"relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-blue-100 to-cyan-100\">\n                <Image\n                  src=\"/Denis-Personal-picture.png\"\n                  alt=\"Denis - AI & Automation Specialist\"\n                  fill\n                  className=\"object-cover\"\n                  priority\n                />\n              </div>\n\n              {/* Floating Elements */}\n              <div className=\"absolute -top-4 -right-4 w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg\">\n                AI\n              </div>\n\n              <div className=\"absolute -bottom-4 -left-4 w-16 h-16 bg-cyan-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg\">\n                5+\n              </div>\n\n              <div className=\"absolute top-1/2 -left-6 w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg\">\n                ML\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <div className=\"text-center mt-16\">\n          <div className=\"inline-flex items-center text-gray-500 text-sm\">\n            <div className=\"w-6 h-10 border-2 border-gray-300 rounded-full flex justify-center mr-3\">\n              <div className=\"w-1 h-3 bg-gray-400 rounded-full mt-2 animate-bounce\"></div>\n            </div>\n            Scroll to explore\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AASO,SAAS,KAAK,EAAE,cAAc,EAAa;IAChD,MAAM,oBAAoB;QACxB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;;;;;;gDAA+C;;;;;;;sDAIjE,6LAAC;4CAAG,WAAU;;gDAA8E;8DAE1F,6LAAC;oDAAK,WAAU;8DAA2E;;;;;;gDAAmB;;;;;;;sDAIhH,6LAAC;4CAAE,WAAU;sDAAuE;;;;;;;;;;;;8CAOtF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAKzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS;4CACT,WAAU;;gDACX;8DAEC,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAGxB,6LAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOvC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;4CACV,QAAQ;;;;;;;;;;;kDAKZ,6LAAC;wCAAI,WAAU;kDAAsI;;;;;;kDAIrJ,6LAAC;wCAAI,WAAU;kDAAgI;;;;;;kDAI/I,6LAAC;wCAAI,WAAU;kDAAwI;;;;;;;;;;;;;;;;;;;;;;;8BAQ7J,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;4BACX;;;;;;;;;;;;;;;;;;;;;;;AAOlB;KA9GgB", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Header } from '@/components/Header';\nimport { Hero } from '@/components/Hero';\nimport { BlogSection } from '@/components/BlogSection';\nimport { PersonalStory } from '@/components/PersonalStory';\nimport { CaseStudyShowcase } from '@/components/CaseStudyShowcase';\nimport { TestimonialWall } from '@/components/TestimonialWall';\nimport { FAQ } from '@/components/FAQ';\nimport { Footer } from '@/components/Footer';\nimport { ContactModal } from '@/components/ContactModal';\n\nexport default function Home() {\n  const [isContactModalOpen, setIsContactModalOpen] = useState(false);\n\n  const handleContactClick = () => {\n    setIsContactModalOpen(true);\n  };\n\n  const handleContactClose = () => {\n    setIsContactModalOpen(false);\n  };\n\n  return (\n    <main className=\"min-h-screen bg-white text-gray-900\">\n      <Header onContactClick={handleContactClick} />\n\n      <Hero onContactClick={handleContactClick} />\n\n      {/* Temporary placeholder sections - will rebuild to match samples */}\n      <section id=\"about\" className=\"py-20 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">About Section</h2>\n          <p className=\"text-gray-600\">Rebuilding to match sample design...</p>\n        </div>\n      </section>\n\n      <section id=\"services\" className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Services Section</h2>\n          <p className=\"text-gray-600\">Rebuilding to match sample design...</p>\n        </div>\n      </section>\n\n      <section id=\"portfolio\" className=\"py-20 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Portfolio Section</h2>\n          <p className=\"text-gray-600\">Rebuilding to match sample design...</p>\n        </div>\n      </section>\n\n      <section id=\"testimonials\" className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Testimonials Section</h2>\n          <p className=\"text-gray-600\">Rebuilding to match sample design...</p>\n        </div>\n      </section>\n\n      <section id=\"contact\" className=\"py-20 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Contact Section</h2>\n          <p className=\"text-gray-600\">Rebuilding to match sample design...</p>\n        </div>\n      </section>\n\n      <ContactModal\n        isOpen={isContactModalOpen}\n        onClose={handleContactClose}\n      />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAOA;;;AAXA;;;;;AAae,SAAS;;IACtB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,qBAAqB;QACzB,sBAAsB;IACxB;IAEA,MAAM,qBAAqB;QACzB,sBAAsB;IACxB;IAEA,qBACE,6LAAC;QAAK,WAAU;;0BACd,6LAAC,+HAAA,CAAA,SAAM;gBAAC,gBAAgB;;;;;;0BAExB,6LAAC,6HAAA,CAAA,OAAI;gBAAC,gBAAgB;;;;;;0BAGtB,6LAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAIjC,6LAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAIjC,6LAAC;gBAAQ,IAAG;gBAAY,WAAU;0BAChC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAIjC,6LAAC;gBAAQ,IAAG;gBAAe,WAAU;0BACnC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAIjC,6LAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAIjC,6LAAC,qIAAA,CAAA,eAAY;gBACX,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAIjB;GA3DwB;KAAA", "debugId": null}}]}