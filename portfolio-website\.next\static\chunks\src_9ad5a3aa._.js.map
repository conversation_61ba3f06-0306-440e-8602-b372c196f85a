{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date: string | Date): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-');\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength).trim() + '...';\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function getRandomItems<T>(array: T[], count: number): T[] {\n  const shuffled = [...array].sort(() => 0.5 - Math.random());\n  return shuffled.slice(0, count);\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,IAAI,KAAK;AAC3C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,eAAkB,KAAU,EAAE,KAAa;IACzD,MAAM,WAAW;WAAI;KAAM,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IACxD,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useTheme } from 'next-themes';\nimport { Moon, Sun, Menu, X } from 'lucide-react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { cn } from '@/lib/utils';\nimport type { NavigationItem } from '@/types';\n\ninterface HeaderProps {\n  onContactClick: () => void;\n}\n\nconst navigationItems: NavigationItem[] = [\n  { id: 'home', label: 'Home', href: '#home' },\n  { id: 'services', label: 'Services', href: '#services' },\n  { id: 'portfolio', label: 'Portfolio', href: '#portfolio' },\n  { id: 'reviews', label: 'Reviews', href: '#reviews' },\n  { id: 'faq', label: 'FAQ', href: '#faq' },\n];\n\nexport function Header({ onContactClick }: HeaderProps) {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [activeSection, setActiveSection] = useState('home');\n  const { theme, setTheme } = useTheme();\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 20);\n    };\n\n    const handleSectionChange = () => {\n      const sections = navigationItems.map(item => item.id);\n      const currentSection = sections.find(section => {\n        const element = document.getElementById(section);\n        if (element) {\n          const rect = element.getBoundingClientRect();\n          return rect.top <= 100 && rect.bottom >= 100;\n        }\n        return false;\n      });\n      \n      if (currentSection) {\n        setActiveSection(currentSection);\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    window.addEventListener('scroll', handleSectionChange);\n    \n    return () => {\n      window.removeEventListener('scroll', handleScroll);\n      window.removeEventListener('scroll', handleSectionChange);\n    };\n  }, []);\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n      setIsMobileMenuOpen(false);\n    }\n  };\n\n  const toggleTheme = () => {\n    setTheme(theme === 'dark' ? 'light' : 'dark');\n  };\n\n  if (!mounted) {\n    return null;\n  }\n\n  return (\n    <motion.header\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      className={cn(\n        'fixed top-0 left-0 right-0 z-40 transition-all duration-300',\n        isScrolled\n          ? 'bg-white/80 dark:bg-dark-background/80 backdrop-blur-md shadow-lg'\n          : 'bg-transparent'\n      )}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo */}\n          <motion.div\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: 0.1 }}\n            className=\"flex-shrink-0\"\n          >\n            <a\n              href=\"#home\"\n              onClick={(e) => {\n                e.preventDefault();\n                scrollToSection('#home');\n              }}\n              className=\"text-2xl lg:text-3xl font-bold gradient-text hover:scale-105 transition-transform duration-300\"\n            >\n              DENIS\n            </a>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            {navigationItems.map((item, index) => (\n              <motion.a\n                key={item.id}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.1 + index * 0.1 }}\n                href={item.href}\n                onClick={(e) => {\n                  e.preventDefault();\n                  scrollToSection(item.href);\n                }}\n                className={cn(\n                  'relative px-3 py-2 text-sm font-medium transition-colors duration-300',\n                  activeSection === item.id\n                    ? 'text-dark-primary dark:text-dark-primary'\n                    : 'text-light-body dark:text-dark-body hover:text-light-primary dark:hover:text-dark-primary'\n                )}\n              >\n                {item.label}\n                {activeSection === item.id && (\n                  <motion.div\n                    layoutId=\"activeSection\"\n                    className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-primary\"\n                    initial={false}\n                    transition={{ type: \"spring\", stiffness: 380, damping: 30 }}\n                  />\n                )}\n              </motion.a>\n            ))}\n          </nav>\n\n          {/* Right Side Actions */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Theme Toggle */}\n            <motion.button\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.3 }}\n              onClick={toggleTheme}\n              className=\"p-2 rounded-lg bg-light-card dark:bg-dark-card hover:bg-light-primary/10 dark:hover:bg-dark-primary/10 transition-colors duration-300\"\n              aria-label=\"Toggle theme\"\n            >\n              {theme === 'dark' ? (\n                <Sun className=\"w-5 h-5 text-dark-primary\" />\n              ) : (\n                <Moon className=\"w-5 h-5 text-light-primary\" />\n              )}\n            </motion.button>\n\n            {/* Get In Touch Button */}\n            <motion.button\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.4 }}\n              onClick={onContactClick}\n              className=\"hidden sm:inline-flex items-center px-6 py-2.5 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-medium rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105\"\n            >\n              Get In Touch\n            </motion.button>\n\n            {/* Mobile Menu Button */}\n            <motion.button\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.5 }}\n              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n              className=\"lg:hidden p-2 rounded-lg bg-light-card dark:bg-dark-card hover:bg-light-primary/10 dark:hover:bg-dark-primary/10 transition-colors duration-300\"\n              aria-label=\"Toggle mobile menu\"\n            >\n              {isMobileMenuOpen ? (\n                <X className=\"w-5 h-5 text-light-body dark:text-dark-body\" />\n              ) : (\n                <Menu className=\"w-5 h-5 text-light-body dark:text-dark-body\" />\n              )}\n            </motion.button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      <AnimatePresence>\n        {isMobileMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"lg:hidden bg-white/95 dark:bg-dark-background/95 backdrop-blur-md border-t border-light-primary/20 dark:border-dark-primary/20\"\n          >\n            <div className=\"max-w-7xl mx-auto px-4 py-4\">\n              <nav className=\"flex flex-col space-y-2\">\n                {navigationItems.map((item) => (\n                  <a\n                    key={item.id}\n                    href={item.href}\n                    onClick={(e) => {\n                      e.preventDefault();\n                      scrollToSection(item.href);\n                    }}\n                    className={cn(\n                      'px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-300',\n                      activeSection === item.id\n                        ? 'bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary'\n                        : 'text-light-body dark:text-dark-body hover:bg-light-primary/5 dark:hover:bg-dark-primary/5'\n                    )}\n                  >\n                    {item.label}\n                  </a>\n                ))}\n                <button\n                  onClick={() => {\n                    onContactClick();\n                    setIsMobileMenuOpen(false);\n                  }}\n                  className=\"mt-4 w-full px-4 py-3 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-medium rounded-lg hover:shadow-neon transition-all duration-300\"\n                >\n                  Get In Touch\n                </button>\n              </nav>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;AANA;;;;;;AAaA,MAAM,kBAAoC;IACxC;QAAE,IAAI;QAAQ,OAAO;QAAQ,MAAM;IAAQ;IAC3C;QAAE,IAAI;QAAY,OAAO;QAAY,MAAM;IAAY;IACvD;QAAE,IAAI;QAAa,OAAO;QAAa,MAAM;IAAa;IAC1D;QAAE,IAAI;QAAW,OAAO;QAAW,MAAM;IAAW;IACpD;QAAE,IAAI;QAAO,OAAO;QAAO,MAAM;IAAO;CACzC;AAEM,SAAS,OAAO,EAAE,cAAc,EAAe;;IACpD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,WAAW;QACb;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,MAAM;wDAAsB;oBAC1B,MAAM,WAAW,gBAAgB,GAAG;yEAAC,CAAA,OAAQ,KAAK,EAAE;;oBACpD,MAAM,iBAAiB,SAAS,IAAI;+EAAC,CAAA;4BACnC,MAAM,UAAU,SAAS,cAAc,CAAC;4BACxC,IAAI,SAAS;gCACX,MAAM,OAAO,QAAQ,qBAAqB;gCAC1C,OAAO,KAAK,GAAG,IAAI,OAAO,KAAK,MAAM,IAAI;4BAC3C;4BACA,OAAO;wBACT;;oBAEA,IAAI,gBAAgB;wBAClB,iBAAiB;oBACnB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,UAAU;YAElC;oCAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;2BAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;YAC5C,oBAAoB;QACtB;IACF;IAEA,MAAM,cAAc;QAClB,SAAS,UAAU,SAAS,UAAU;IACxC;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,sEACA;;0BAGN,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCAEV,cAAA,6LAAC;gCACC,MAAK;gCACL,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,gBAAgB;gCAClB;gCACA,WAAU;0CACX;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,MAAM,sBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,MAAM,QAAQ;oCAAI;oCACvC,MAAM,KAAK,IAAI;oCACf,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,gBAAgB,KAAK,IAAI;oCAC3B;oCACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yEACA,kBAAkB,KAAK,EAAE,GACrB,6CACA;;wCAGL,KAAK,KAAK;wCACV,kBAAkB,KAAK,EAAE,kBACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,UAAS;4CACT,WAAU;4CACV,SAAS;4CACT,YAAY;gDAAE,MAAM;gDAAU,WAAW;gDAAK,SAAS;4CAAG;;;;;;;mCAtBzD,KAAK,EAAE;;;;;;;;;;sCA8BlB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;oCAAI;oCACzB,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEV,UAAU,uBACT,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;6DAEf,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAKpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;oCAAI;oCACzB,SAAS;oCACT,WAAU;8CACX;;;;;;8CAKD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;oCAAI;oCACzB,SAAS,IAAM,oBAAoB,CAAC;oCACpC,WAAU;oCACV,cAAW;8CAEV,iCACC,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;6DAEb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1B,6LAAC,4LAAA,CAAA,kBAAe;0BACb,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC;wCAEC,MAAM,KAAK,IAAI;wCACf,SAAS,CAAC;4CACR,EAAE,cAAc;4CAChB,gBAAgB,KAAK,IAAI;wCAC3B;wCACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2EACA,kBAAkB,KAAK,EAAE,GACrB,0FACA;kDAGL,KAAK,KAAK;uCAbN,KAAK,EAAE;;;;;8CAgBhB,6LAAC;oCACC,SAAS;wCACP;wCACA,oBAAoB;oCACtB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAxNgB;;QAIc,mJAAA,CAAA,WAAQ;;;KAJtB", "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { ArrowRight, Play, Star, Users, TrendingUp } from 'lucide-react';\n\ninterface HeroProps {\n  onContactClick: () => void;\n}\n\nconst platformIcons = [\n  { name: 'WordPress', icon: '🔧' },\n  { name: 'Google Analytics', icon: '📊' },\n  { name: 'OpenAI', icon: '🤖' },\n  { name: 'Automation', icon: '⚡' },\n  { name: 'Data Science', icon: '📈' },\n];\n\nconst stats = [\n  { label: 'Projects Completed', value: '150+', icon: TrendingUp },\n  { label: 'Happy Clients', value: '50+', icon: Users },\n  { label: 'Success Rate', value: '98%', icon: Star },\n];\n\nexport function Hero({ onContactClick }: HeroProps) {\n  const scrollToPortfolio = () => {\n    const element = document.querySelector('#portfolio');\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section id=\"home\" className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-light-background dark:bg-dark-background\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-light-primary/5 via-transparent to-light-primary/10 dark:from-dark-primary/5 dark:to-dark-primary/10\" />\n      \n      {/* Floating Elements */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(6)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-light-primary/20 dark:bg-dark-primary/20 rounded-full\"\n            animate={{\n              y: [0, -100, 0],\n              x: [0, 50, 0],\n              opacity: [0.3, 0.8, 0.3],\n            }}\n            transition={{\n              duration: 6 + i,\n              repeat: Infinity,\n              delay: i * 0.5,\n            }}\n            style={{\n              left: `${10 + i * 15}%`,\n              top: `${20 + i * 10}%`,\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16\">\n        <div className=\"grid lg:grid-cols-2 gap-12 lg:gap-16 items-center\">\n          {/* Left Content */}\n          <div className=\"text-center lg:text-left\">\n            <motion.div\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8 }}\n              className=\"mb-6\"\n            >\n              <span className=\"inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4\">\n                🚀 AI & Automation Specialist\n              </span>\n              \n              <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6\">\n                <span className=\"gradient-text\">Transform Your Business</span>\n                <br />\n                <span className=\"text-light-h2 dark:text-dark-h2\">\n                  with Intelligent\n                </span>\n                <br />\n                <span className=\"gradient-text\">Automation</span>\n              </h1>\n              \n              <p className=\"text-lg sm:text-xl text-light-secondary dark:text-dark-secondary max-w-2xl mx-auto lg:mx-0 leading-relaxed\">\n                From Kenya to global success, I help businesses unlock their potential through \n                cutting-edge AI solutions and automation strategies that deliver measurable results.\n              </p>\n            </motion.div>\n\n            {/* Stats */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"grid grid-cols-3 gap-4 mb-8\"\n            >\n              {stats.map((stat, index) => (\n                <div key={stat.label} className=\"text-center\">\n                  <div className=\"flex items-center justify-center mb-2\">\n                    <stat.icon className=\"w-5 h-5 text-light-primary dark:text-dark-primary mr-2\" />\n                    <span className=\"text-2xl font-bold gradient-text\">{stat.value}</span>\n                  </div>\n                  <p className=\"text-sm text-light-secondary dark:text-dark-secondary\">{stat.label}</p>\n                </div>\n              ))}\n            </motion.div>\n\n            {/* CTAs */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\"\n            >\n              <button\n                onClick={scrollToPortfolio}\n                className=\"group inline-flex items-center px-8 py-4 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-semibold rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105\"\n              >\n                View Case Studies\n                <ArrowRight className=\"ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n              </button>\n              \n              <button\n                onClick={onContactClick}\n                className=\"group inline-flex items-center px-8 py-4 border-2 border-light-primary dark:border-dark-primary text-light-primary dark:text-dark-primary font-semibold rounded-lg hover:bg-light-primary dark:hover:bg-dark-primary hover:text-white transition-all duration-300\"\n              >\n                <Play className=\"mr-2 w-5 h-5\" />\n                Book a Call\n              </button>\n            </motion.div>\n          </div>\n\n          {/* Right Content - Image & Platform Icons */}\n          <div className=\"relative\">\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.8, delay: 0.3 }}\n              className=\"relative\"\n            >\n              {/* Main Image */}\n              <div className=\"relative w-80 h-80 mx-auto lg:w-96 lg:h-96\">\n                <div className=\"absolute inset-0 bg-gradient-primary rounded-full opacity-20 animate-pulse\" />\n                <div className=\"relative w-full h-full rounded-full overflow-hidden border-4 border-light-primary dark:border-dark-primary shadow-2xl\">\n                  <Image\n                    src=\"/Denis-Personal-picture.png\"\n                    alt=\"Denis - AI & Automation Specialist\"\n                    fill\n                    className=\"object-cover\"\n                    priority\n                  />\n                </div>\n              </div>\n\n              {/* Floating Platform Icons */}\n              {platformIcons.map((platform, index) => (\n                <motion.div\n                  key={platform.name}\n                  initial={{ opacity: 0, scale: 0 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ \n                    duration: 0.5, \n                    delay: 0.6 + index * 0.1,\n                    type: \"spring\",\n                    stiffness: 200 \n                  }}\n                  className=\"absolute w-16 h-16 bg-white dark:bg-dark-card rounded-full shadow-lg flex items-center justify-center text-2xl hover:scale-110 transition-transform duration-300 cursor-pointer\"\n                  style={{\n                    top: `${15 + Math.sin(index * 1.2) * 30}%`,\n                    left: `${10 + Math.cos(index * 1.2) * 35}%`,\n                    transform: index % 2 === 0 ? 'translateX(100%)' : 'translateX(-50%)',\n                  }}\n                  animate={{\n                    y: [0, -10, 0],\n                  }}\n                  transition={{\n                    duration: 3 + index * 0.5,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                  }}\n                  title={platform.name}\n                >\n                  {platform.icon}\n                </motion.div>\n              ))}\n            </motion.div>\n\n            {/* Experience Badge */}\n            <motion.div\n              initial={{ opacity: 0, x: 50 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.8, delay: 0.8 }}\n              className=\"absolute -bottom-6 -right-6 bg-white dark:bg-dark-card rounded-2xl p-4 shadow-xl border border-light-primary/20 dark:border-dark-primary/20\"\n            >\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold gradient-text\">5+</div>\n                <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Years Experience</div>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.8, delay: 1 }}\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n        >\n          <motion.div\n            animate={{ y: [0, 10, 0] }}\n            transition={{ duration: 2, repeat: Infinity }}\n            className=\"w-6 h-10 border-2 border-light-primary dark:border-dark-primary rounded-full flex justify-center\"\n          >\n            <motion.div\n              animate={{ y: [0, 12, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n              className=\"w-1 h-3 bg-light-primary dark:bg-dark-primary rounded-full mt-2\"\n            />\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAUA,MAAM,gBAAgB;IACpB;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAI;IAChC;QAAE,MAAM;QAAgB,MAAM;IAAK;CACpC;AAED,MAAM,QAAQ;IACZ;QAAE,OAAO;QAAsB,OAAO;QAAQ,MAAM,qNAAA,CAAA,aAAU;IAAC;IAC/D;QAAE,OAAO;QAAiB,OAAO;QAAO,MAAM,uMAAA,CAAA,QAAK;IAAC;IACpD;QAAE,OAAO;QAAgB,OAAO;QAAO,MAAM,qMAAA,CAAA,OAAI;IAAC;CACnD;AAEM,SAAS,KAAK,EAAE,cAAc,EAAa;IAChD,MAAM,oBAAoB;QACxB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;;0BAE3B,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,SAAS;4BACP,GAAG;gCAAC;gCAAG,CAAC;gCAAK;6BAAE;4BACf,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;4BACb,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;wBAC1B;wBACA,YAAY;4BACV,UAAU,IAAI;4BACd,QAAQ;4BACR,OAAO,IAAI;wBACb;wBACA,OAAO;4BACL,MAAM,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;4BACvB,KAAK,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;wBACxB;uBAfK;;;;;;;;;;0BAoBX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAqJ;;;;;;0DAIrK,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;;;;;kEACD,6LAAC;wDAAK,WAAU;kEAAkC;;;;;;kEAGlD,6LAAC;;;;;kEACD,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;0DAGlC,6LAAC;gDAAE,WAAU;0DAA6G;;;;;;;;;;;;kDAO5H,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;gDAAqB,WAAU;;kEAC9B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;0EACrB,6LAAC;gEAAK,WAAU;0EAAoC,KAAK,KAAK;;;;;;;;;;;;kEAEhE,6LAAC;wDAAE,WAAU;kEAAyD,KAAK,KAAK;;;;;;;+CALxE,KAAK,KAAK;;;;;;;;;;kDAWxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAEV,6LAAC;gDACC,SAAS;gDACT,WAAU;;oDACX;kEAEC,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;;0DAGxB,6LAAC;gDACC,SAAS;gDACT,WAAU;;kEAEV,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAOvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;;0DAGV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4DACJ,KAAI;4DACJ,KAAI;4DACJ,IAAI;4DACJ,WAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;4CAMb,cAAc,GAAG,CAAC,CAAC,UAAU,sBAC5B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,SAAS;wDAAE,SAAS;wDAAG,OAAO;oDAAE;oDAChC,YAAY;wDACV,UAAU;wDACV,OAAO,MAAM,QAAQ;wDACrB,MAAM;wDACN,WAAW;oDACb;oDACA,WAAU;oDACV,OAAO;wDACL,KAAK,GAAG,KAAK,KAAK,GAAG,CAAC,QAAQ,OAAO,GAAG,CAAC,CAAC;wDAC1C,MAAM,GAAG,KAAK,KAAK,GAAG,CAAC,QAAQ,OAAO,GAAG,CAAC,CAAC;wDAC3C,WAAW,QAAQ,MAAM,IAAI,qBAAqB;oDACpD;oDACA,SAAS;wDACP,GAAG;4DAAC;4DAAG,CAAC;4DAAI;yDAAE;oDAChB;oDACA,YAAY;wDACV,UAAU,IAAI,QAAQ;wDACtB,QAAQ;wDACR,MAAM;oDACR;oDACA,OAAO,SAAS,IAAI;8DAEnB,SAAS,IAAI;mDAzBT,SAAS,IAAI;;;;;;;;;;;kDA+BxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,6LAAC;oDAAI,WAAU;8DAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAE;wBACtC,WAAU;kCAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;oCAAC;oCAAG;oCAAI;iCAAE;4BAAC;4BACzB,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;4BAC5C,WAAU;sCAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;gCAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB;KA1MgB", "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/BlogSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { Clock, ArrowRight, Tag } from 'lucide-react';\nimport type { BlogPost } from '@/types';\n\nconst blogPosts: BlogPost[] = [\n  {\n    id: '1',\n    title: 'The Future of AI in Business Automation',\n    excerpt: 'Discover how artificial intelligence is revolutionizing business processes and creating unprecedented opportunities for growth and efficiency.',\n    readTime: '5 min read',\n    category: 'AI Strategy',\n    image: '/placeholder.png',\n    slug: 'future-ai-business-automation',\n    publishedAt: '2024-01-15',\n  },\n  {\n    id: '2',\n    title: 'Building Scalable Automation Workflows',\n    excerpt: 'Learn the essential principles and best practices for creating automation systems that grow with your business needs.',\n    readTime: '7 min read',\n    category: 'Automation',\n    image: '/placeholder.png',\n    slug: 'scalable-automation-workflows',\n    publishedAt: '2024-01-10',\n  },\n  {\n    id: '3',\n    title: 'Data-Driven Decision Making with AI',\n    excerpt: 'Transform your business intelligence with AI-powered analytics that provide actionable insights and predictive capabilities.',\n    readTime: '6 min read',\n    category: 'Data Science',\n    image: '/placeholder.png',\n    slug: 'data-driven-ai-decisions',\n    publishedAt: '2024-01-05',\n  },\n];\n\nconst categoryColors = {\n  'AI Strategy': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',\n  'Automation': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',\n  'Data Science': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',\n};\n\nexport function BlogSection() {\n  return (\n    <section id=\"insights\" className=\"py-20 bg-light-background dark:bg-dark-background\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <span className=\"inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4\">\n            📚 Latest Insights\n          </span>\n          \n          <h2 className=\"text-4xl sm:text-5xl font-bold gradient-text mb-6\">\n            Latest AI and Automation Insights\n          </h2>\n          \n          <p className=\"text-lg text-light-secondary dark:text-dark-secondary max-w-3xl mx-auto\">\n            Stay ahead of the curve with cutting-edge insights, practical strategies, and real-world \n            applications of AI and automation technologies.\n          </p>\n        </motion.div>\n\n        {/* Blog Cards Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {blogPosts.map((post, index) => (\n            <motion.article\n              key={post.id}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group bg-white dark:bg-dark-card rounded-2xl overflow-hidden shadow-card hover:shadow-card-hover transition-all duration-300 hover:-translate-y-2\"\n            >\n              {/* Image */}\n              <div className=\"relative h-48 overflow-hidden\">\n                <Image\n                  src={post.image}\n                  alt={post.title}\n                  fill\n                  className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\" />\n                \n                {/* Category Tag */}\n                <div className=\"absolute top-4 left-4\">\n                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${\n                    categoryColors[post.category as keyof typeof categoryColors]\n                  }`}>\n                    <Tag className=\"w-3 h-3 mr-1\" />\n                    {post.category}\n                  </span>\n                </div>\n              </div>\n\n              {/* Content */}\n              <div className=\"p-6\">\n                <div className=\"flex items-center text-sm text-light-secondary dark:text-dark-secondary mb-3\">\n                  <Clock className=\"w-4 h-4 mr-1\" />\n                  {post.readTime}\n                  <span className=\"mx-2\">•</span>\n                  <time dateTime={post.publishedAt}>\n                    {new Date(post.publishedAt).toLocaleDateString('en-US', {\n                      month: 'short',\n                      day: 'numeric',\n                      year: 'numeric',\n                    })}\n                  </time>\n                </div>\n\n                <h3 className=\"text-xl font-bold text-light-h2 dark:text-dark-h2 mb-3 group-hover:text-light-primary dark:group-hover:text-dark-primary transition-colors\">\n                  {post.title}\n                </h3>\n\n                <p className=\"text-light-secondary dark:text-dark-secondary mb-4 line-clamp-3\">\n                  {post.excerpt}\n                </p>\n\n                {/* Read More Link */}\n                <button className=\"group/btn inline-flex items-center text-light-primary dark:text-dark-primary font-medium hover:text-light-primary-deep dark:hover:text-dark-primary-deep transition-colors\">\n                  Read More\n                  <ArrowRight className=\"ml-2 w-4 h-4 group-hover/btn:translate-x-1 transition-transform\" />\n                </button>\n              </div>\n            </motion.article>\n          ))}\n        </div>\n\n        {/* View All Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-12\"\n        >\n          <button className=\"inline-flex items-center px-8 py-4 bg-light-card dark:bg-dark-card border-2 border-light-primary dark:border-dark-primary text-light-primary dark:text-dark-primary font-semibold rounded-lg hover:bg-light-primary dark:hover:bg-dark-primary hover:text-white transition-all duration-300 hover:scale-105\">\n            View All Articles\n            <ArrowRight className=\"ml-2 w-5 h-5\" />\n          </button>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAOA,MAAM,YAAwB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;IACf;CACD;AAED,MAAM,iBAAiB;IACrB,eAAe;IACf,cAAc;IACd,gBAAgB;AAClB;AAEO,SAAS;IACd,qBACE,6LAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAK,WAAU;sCAAqJ;;;;;;sCAIrK,6LAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAIlE,6LAAC;4BAAE,WAAU;sCAA0E;;;;;;;;;;;;8BAOzF,6LAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;4BAEb,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,KAAK,KAAK;4CACf,KAAK,KAAK,KAAK;4CACf,IAAI;4CACJ,WAAU;;;;;;sDAEZ,6LAAC;4CAAI,WAAU;;;;;;sDAGf,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAW,CAAC,oEAAoE,EACpF,cAAc,CAAC,KAAK,QAAQ,CAAgC,EAC5D;;kEACA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACd,KAAK,QAAQ;;;;;;;;;;;;;;;;;;8CAMpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAChB,KAAK,QAAQ;8DACd,6LAAC;oDAAK,WAAU;8DAAO;;;;;;8DACvB,6LAAC;oDAAK,UAAU,KAAK,WAAW;8DAC7B,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC,SAAS;wDACtD,OAAO;wDACP,KAAK;wDACL,MAAM;oDACR;;;;;;;;;;;;sDAIJ,6LAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAGb,6LAAC;4CAAE,WAAU;sDACV,KAAK,OAAO;;;;;;sDAIf,6LAAC;4CAAO,WAAU;;gDAA6K;8DAE7L,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;2BAtDrB,KAAK,EAAE;;;;;;;;;;8BA8DlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAO,WAAU;;4BAA8S;0CAE9T,6LAAC,qNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KA3GgB", "debugId": null}}, {"offset": {"line": 1370, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/PersonalStory.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { MapPin, TrendingUp, Users, Award, ArrowRight } from 'lucide-react';\n\nconst highlights = [\n  {\n    icon: MapPin,\n    title: 'From Kenya to Global',\n    description: 'Started in Nairobi, now serving clients worldwide',\n  },\n  {\n    icon: TrendingUp,\n    title: '150+ Projects Delivered',\n    description: 'Consistent track record of successful implementations',\n  },\n  {\n    icon: Users,\n    title: '50+ Happy Clients',\n    description: 'Building lasting relationships through exceptional results',\n  },\n  {\n    icon: Award,\n    title: '98% Success Rate',\n    description: 'Proven methodology that delivers measurable outcomes',\n  },\n];\n\nconst journeySteps = [\n  {\n    year: '2019',\n    title: 'The Beginning',\n    description: 'Started as a curious developer in Nairobi, fascinated by the potential of automation',\n  },\n  {\n    year: '2021',\n    title: 'First Breakthrough',\n    description: 'Developed my first AI-powered business solution, saving a client 40 hours per week',\n  },\n  {\n    year: '2022',\n    title: 'Global Expansion',\n    description: '<PERSON>gan working with international clients, scaling solutions across different markets',\n  },\n  {\n    year: '2024',\n    title: 'AI Specialist',\n    description: 'Now recognized as a leading expert in AI and automation solutions',\n  },\n];\n\nexport function PersonalStory() {\n  return (\n    <section id=\"story\" className=\"py-20 bg-light-card dark:bg-dark-card\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n          {/* Left Side - Image/Visual */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"relative\"\n          >\n            {/* Main Image */}\n            <div className=\"relative\">\n              <div className=\"relative w-full h-96 rounded-2xl overflow-hidden shadow-2xl\">\n                <Image\n                  src=\"/placeholder.png\"\n                  alt=\"Denis working on AI solutions\"\n                  fill\n                  className=\"object-cover\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent\" />\n              </div>\n              \n              {/* Floating Stats */}\n              <motion.div\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.8, delay: 0.3 }}\n                viewport={{ once: true }}\n                className=\"absolute -top-6 -right-6 bg-white dark:bg-dark-background rounded-2xl p-6 shadow-xl border border-light-primary/20 dark:border-dark-primary/20\"\n              >\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold gradient-text\">5+</div>\n                  <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Years</div>\n                  <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Experience</div>\n                </div>\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.8, delay: 0.5 }}\n                viewport={{ once: true }}\n                className=\"absolute -bottom-6 -left-6 bg-white dark:bg-dark-background rounded-2xl p-6 shadow-xl border border-light-primary/20 dark:border-dark-primary/20\"\n              >\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold gradient-text\">98%</div>\n                  <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Success</div>\n                  <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Rate</div>\n                </div>\n              </motion.div>\n            </div>\n          </motion.div>\n\n          {/* Right Side - Story Content */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            {/* Header */}\n            <div>\n              <span className=\"inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4\">\n                🚀 My Journey\n              </span>\n              \n              <h2 className=\"text-4xl sm:text-5xl font-bold mb-6\">\n                <span className=\"text-light-h2 dark:text-dark-h2\">The Journey to</span>\n                <br />\n                <span className=\"gradient-text\">AI Specialist</span>\n              </h2>\n              \n              <h3 className=\"text-2xl font-semibold text-light-h3 dark:text-dark-h3 mb-6\">\n                Turning Challenges Into Opportunities\n              </h3>\n            </div>\n\n            {/* Story Content */}\n            <div className=\"space-y-6\">\n              <p className=\"text-lg text-light-body dark:text-dark-body leading-relaxed\">\n                My journey began in Nairobi, Kenya, where I discovered the transformative power of \n                technology. What started as curiosity about automation quickly evolved into a passion \n                for solving complex business challenges through AI and intelligent systems.\n              </p>\n              \n              <p className=\"text-lg text-light-body dark:text-dark-body leading-relaxed\">\n                From those early days of experimenting with simple scripts to now architecting \n                enterprise-level AI solutions, every project has been a stepping stone toward \n                mastering the art of digital transformation.\n              </p>\n              \n              <p className=\"text-lg text-light-body dark:text-dark-body leading-relaxed\">\n                Today, I help businesses worldwide unlock their potential through cutting-edge \n                automation strategies, proving that with the right approach, any challenge can \n                become an opportunity for growth.\n              </p>\n            </div>\n\n            {/* Highlights Grid */}\n            <div className=\"grid grid-cols-2 gap-4 mt-8\">\n              {highlights.map((highlight, index) => (\n                <motion.div\n                  key={highlight.title}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"p-4 bg-white dark:bg-dark-background rounded-xl border border-light-primary/10 dark:border-dark-primary/10\"\n                >\n                  <highlight.icon className=\"w-6 h-6 text-light-primary dark:text-dark-primary mb-2\" />\n                  <h4 className=\"font-semibold text-light-h3 dark:text-dark-h3 text-sm mb-1\">\n                    {highlight.title}\n                  </h4>\n                  <p className=\"text-xs text-light-secondary dark:text-dark-secondary\">\n                    {highlight.description}\n                  </p>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* CTA */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              viewport={{ once: true }}\n              className=\"pt-6\"\n            >\n              <button className=\"group inline-flex items-center px-8 py-4 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-semibold rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105\">\n                Let's Build Something Amazing\n                <ArrowRight className=\"ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n              </button>\n            </motion.div>\n          </motion.div>\n        </div>\n\n        {/* Journey Timeline */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"mt-20\"\n        >\n          <h3 className=\"text-3xl font-bold text-center gradient-text mb-12\">\n            The Journey Timeline\n          </h3>\n          \n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {journeySteps.map((step, index) => (\n              <motion.div\n                key={step.year}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"text-center\"\n              >\n                <div className=\"relative\">\n                  <div className=\"w-16 h-16 mx-auto bg-gradient-primary-button dark:bg-gradient-primary-button rounded-full flex items-center justify-center text-white font-bold text-lg mb-4\">\n                    {step.year}\n                  </div>\n                  {index < journeySteps.length - 1 && (\n                    <div className=\"hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-light-primary to-light-primary/20 dark:from-dark-primary dark:to-dark-primary/20\" />\n                  )}\n                </div>\n                <h4 className=\"font-bold text-light-h3 dark:text-dark-h3 mb-2\">\n                  {step.title}\n                </h4>\n                <p className=\"text-sm text-light-secondary dark:text-dark-secondary\">\n                  {step.description}\n                </p>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,aAAa;IACjB;QACE,MAAM,6MAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,qNAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,eAAe;IACnB;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;CACD;AAEM,SAAS;IACd,qBACE,6LAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAGV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,6LAAC;oDAAI,WAAU;8DAAwD;;;;;;8DACvE,6LAAC;oDAAI,WAAU;8DAAwD;;;;;;;;;;;;;;;;;kDAI3E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,6LAAC;oDAAI,WAAU;8DAAwD;;;;;;8DACvE,6LAAC;oDAAI,WAAU;8DAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO/E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAGV,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAqJ;;;;;;sDAIrK,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAkC;;;;;;8DAClD,6LAAC;;;;;8DACD,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAGlC,6LAAC;4CAAG,WAAU;sDAA8D;;;;;;;;;;;;8CAM9E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA8D;;;;;;sDAM3E,6LAAC;4CAAE,WAAU;sDAA8D;;;;;;sDAM3E,6LAAC;4CAAE,WAAU;sDAA8D;;;;;;;;;;;;8CAQ7E,6LAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,6LAAC,UAAU,IAAI;oDAAC,WAAU;;;;;;8DAC1B,6LAAC;oDAAG,WAAU;8DACX,UAAU,KAAK;;;;;;8DAElB,6LAAC;oDAAE,WAAU;8DACV,UAAU,WAAW;;;;;;;2CAZnB,UAAU,KAAK;;;;;;;;;;8CAmB1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;8CAEV,cAAA,6LAAC;wCAAO,WAAU;;4CAAwM;0DAExN,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAInE,6LAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI;;;;;;gDAEX,QAAQ,aAAa,MAAM,GAAG,mBAC7B,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAGnB,6LAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAEb,6LAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;;mCAnBd,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4B9B;KAvLgB", "debugId": null}}, {"offset": {"line": 1942, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/TestimonialWall.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Star, Play, TrendingUp, Users } from 'lucide-react';\nimport Image from 'next/image';\nimport type { TestimonialCard } from '@/types';\n\nconst testimonialCards: TestimonialCard[] = [\n  {\n    id: '1',\n    type: 'text',\n    size: 'medium',\n    content: {\n      text: \"<PERSON> transformed our entire workflow with AI automation. We're now processing 3x more data with half the manual effort!\"\n    },\n    author: {\n      name: '<PERSON>',\n      role: 'Operations Director',\n      avatar: '/placeholder.png',\n      initials: 'SJ'\n    }\n  },\n  {\n    id: '2',\n    type: 'stat',\n    size: 'small',\n    accent: 'gold',\n    content: {\n      stat: '300%',\n      metric: 'Efficiency Increase'\n    },\n    author: {\n      name: 'TechCorp',\n      role: 'Client Result',\n      initials: 'TC'\n    }\n  },\n  {\n    id: '3',\n    type: 'video',\n    size: 'large',\n    content: {\n      text: \"Watch how Denis helped us automate our customer service and reduce response time by 80%\"\n    },\n    author: {\n      name: '<PERSON>',\n      role: 'CEO, InnovateLab',\n      avatar: '/placeholder.png',\n      initials: 'MC'\n    },\n    video: {\n      thumbnail: '/placeholder.png'\n    }\n  },\n  {\n    id: '4',\n    type: 'quote',\n    size: 'medium',\n    content: {\n      quote: \"The ROI from <PERSON>'s AI solutions exceeded our expectations by 250%. Absolutely game-changing!\"\n    },\n    author: {\n      name: 'Emma Rodriguez',\n      role: 'CFO, DataFlow Inc',\n      avatar: '/placeholder.png',\n      initials: 'ER'\n    }\n  },\n  {\n    id: '5',\n    type: 'stat',\n    size: 'small',\n    accent: 'green',\n    content: {\n      stat: '40hrs',\n      metric: 'Saved Weekly'\n    },\n    author: {\n      name: 'StartupXYZ',\n      role: 'Client Result',\n      initials: 'SX'\n    }\n  },\n  {\n    id: '6',\n    type: 'text',\n    size: 'medium',\n    content: {\n      text: \"Professional, innovative, and results-driven. Denis doesn't just deliver solutions, he delivers transformation.\"\n    },\n    author: {\n      name: 'David Park',\n      role: 'CTO, FutureTech',\n      avatar: '/placeholder.png',\n      initials: 'DP'\n    }\n  },\n  {\n    id: '7',\n    type: 'stat',\n    size: 'small',\n    accent: 'gold',\n    content: {\n      stat: '98%',\n      metric: 'Client Satisfaction'\n    },\n    author: {\n      name: 'Overall Rating',\n      role: 'Client Feedback',\n      initials: 'OR'\n    }\n  },\n  {\n    id: '8',\n    type: 'text',\n    size: 'large',\n    content: {\n      text: \"From Kenya to global impact - Denis brings world-class AI expertise with a personal touch that makes all the difference.\"\n    },\n    author: {\n      name: 'Lisa Thompson',\n      role: 'VP Innovation, GlobalCorp',\n      avatar: '/placeholder.png',\n      initials: 'LT'\n    }\n  }\n];\n\n// Duplicate cards for infinite scroll effect\nconst infiniteCards = [...testimonialCards, ...testimonialCards, ...testimonialCards];\n\nfunction TestimonialCard({ card, index }: { card: TestimonialCard; index: number }) {\n  const sizeClasses = {\n    small: 'h-32',\n    medium: 'h-40',\n    large: 'h-48'\n  };\n\n  const accentClasses = {\n    gold: 'border-yellow-400 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20',\n    green: 'border-green-400 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20'\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6, delay: index * 0.1 }}\n      viewport={{ once: true }}\n      className={`\n        ${sizeClasses[card.size]} \n        ${card.accent ? accentClasses[card.accent] : 'bg-white dark:bg-dark-card border-light-primary/20 dark:border-dark-primary/20'}\n        rounded-2xl p-4 border-2 shadow-card hover:shadow-card-hover transition-all duration-300 hover:-translate-y-1 relative overflow-hidden\n      `}\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute top-0 right-0 w-20 h-20 bg-light-primary dark:bg-dark-primary rounded-full -translate-y-10 translate-x-10\" />\n      </div>\n\n      <div className=\"relative h-full flex flex-col\">\n        {/* Content based on type */}\n        {card.type === 'stat' && (\n          <div className=\"flex-1 flex flex-col justify-center text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-1\">\n              {card.content.stat}\n            </div>\n            <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">\n              {card.content.metric}\n            </div>\n          </div>\n        )}\n\n        {card.type === 'text' && (\n          <div className=\"flex-1\">\n            <p className=\"text-sm text-light-body dark:text-dark-body leading-relaxed line-clamp-4\">\n              \"{card.content.text}\"\n            </p>\n          </div>\n        )}\n\n        {card.type === 'quote' && (\n          <div className=\"flex-1\">\n            <div className=\"text-2xl text-light-primary dark:text-dark-primary mb-2\">\"</div>\n            <p className=\"text-sm text-light-body dark:text-dark-body leading-relaxed line-clamp-3\">\n              {card.content.quote}\n            </p>\n          </div>\n        )}\n\n        {card.type === 'video' && (\n          <div className=\"flex-1 relative\">\n            <div className=\"relative h-20 rounded-lg overflow-hidden mb-2\">\n              <Image\n                src={card.video?.thumbnail || '/placeholder.png'}\n                alt=\"Video thumbnail\"\n                fill\n                className=\"object-cover\"\n              />\n              <div className=\"absolute inset-0 bg-black/30 flex items-center justify-center\">\n                <Play className=\"w-6 h-6 text-white\" />\n              </div>\n            </div>\n            <p className=\"text-xs text-light-secondary dark:text-dark-secondary line-clamp-2\">\n              {card.content.text}\n            </p>\n          </div>\n        )}\n\n        {/* Author */}\n        <div className=\"flex items-center mt-3 pt-3 border-t border-light-primary/10 dark:border-dark-primary/10\">\n          <div className=\"w-8 h-8 rounded-full bg-light-primary dark:bg-dark-primary flex items-center justify-center text-white text-xs font-medium mr-3\">\n            {card.author.avatar ? (\n              <Image\n                src={card.author.avatar}\n                alt={card.author.name}\n                width={32}\n                height={32}\n                className=\"rounded-full\"\n              />\n            ) : (\n              card.author.initials\n            )}\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <div className=\"text-xs font-medium text-light-h3 dark:text-dark-h3 truncate\">\n              {card.author.name}\n            </div>\n            <div className=\"text-xs text-light-secondary dark:text-dark-secondary truncate\">\n              {card.author.role}\n            </div>\n          </div>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n\nexport function TestimonialWall() {\n  return (\n    <section id=\"testimonials\" className=\"py-20 bg-light-background dark:bg-dark-background relative overflow-hidden\">\n      {/* Fade overlays */}\n      <div className=\"absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-light-background dark:from-dark-background to-transparent z-10\" />\n      <div className=\"absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-light-background dark:from-dark-background to-transparent z-10\" />\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <span className=\"inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4\">\n            💬 Client Love\n          </span>\n          \n          <h2 className=\"text-4xl sm:text-5xl font-bold gradient-text mb-6\">\n            What People Are Saying\n          </h2>\n          \n          <p className=\"text-lg text-light-secondary dark:text-dark-secondary max-w-3xl mx-auto\">\n            Real feedback from real clients who've experienced the transformative power of AI automation.\n          </p>\n        </motion.div>\n\n        {/* Testimonial Mosaic */}\n        <div className=\"relative\">\n          {/* Left sliding column */}\n          <motion.div\n            animate={{ y: [0, -50, 0] }}\n            transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n            className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\"\n          >\n            {infiniteCards.slice(0, 8).map((card, index) => (\n              <TestimonialCard key={`${card.id}-${index}`} card={card} index={index} />\n            ))}\n          </motion.div>\n\n          {/* Right sliding column (offset) */}\n          <motion.div\n            animate={{ y: [-50, 0, -50] }}\n            transition={{ duration: 25, repeat: Infinity, ease: \"linear\" }}\n            className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6\"\n          >\n            {infiniteCards.slice(4, 12).map((card, index) => (\n              <TestimonialCard key={`${card.id}-offset-${index}`} card={card} index={index} />\n            ))}\n          </motion.div>\n        </div>\n\n        {/* Stats Summary */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 pt-16 border-t border-light-primary/20 dark:border-dark-primary/20\"\n        >\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">150+</div>\n            <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Projects Completed</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">50+</div>\n            <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Happy Clients</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">98%</div>\n            <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Success Rate</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">5+</div>\n            <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Years Experience</div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAOA,MAAM,mBAAsC;IAC1C;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;YACP,MAAM;QACR;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;YACP,MAAM;YACN,QAAQ;QACV;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;YACP,MAAM;QACR;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA,OAAO;YACL,WAAW;QACb;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;YACP,OAAO;QACT;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;YACP,MAAM;YACN,QAAQ;QACV;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;YACP,MAAM;QACR;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;YACP,MAAM;YACN,QAAQ;QACV;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;YACP,MAAM;QACR;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;IACF;CACD;AAED,6CAA6C;AAC7C,MAAM,gBAAgB;OAAI;OAAqB;OAAqB;CAAiB;AAErF,SAAS,gBAAgB,EAAE,IAAI,EAAE,KAAK,EAA4C;IAChF,MAAM,cAAc;QAClB,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,MAAM;QACN,OAAO;IACT;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAI;QAChD,UAAU;YAAE,MAAM;QAAK;QACvB,WAAW,CAAC;QACV,EAAE,WAAW,CAAC,KAAK,IAAI,CAAC,CAAC;QACzB,EAAE,KAAK,MAAM,GAAG,aAAa,CAAC,KAAK,MAAM,CAAC,GAAG,iFAAiF;;MAEhI,CAAC;;0BAGD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;oBAEZ,KAAK,IAAI,KAAK,wBACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,KAAK,OAAO,CAAC,IAAI;;;;;;0CAEpB,6LAAC;gCAAI,WAAU;0CACZ,KAAK,OAAO,CAAC,MAAM;;;;;;;;;;;;oBAKzB,KAAK,IAAI,KAAK,wBACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAA2E;gCACpF,KAAK,OAAO,CAAC,IAAI;gCAAC;;;;;;;;;;;;oBAKzB,KAAK,IAAI,KAAK,yBACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA0D;;;;;;0CACzE,6LAAC;gCAAE,WAAU;0CACV,KAAK,OAAO,CAAC,KAAK;;;;;;;;;;;;oBAKxB,KAAK,IAAI,KAAK,yBACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAK,KAAK,KAAK,EAAE,aAAa;wCAC9B,KAAI;wCACJ,IAAI;wCACJ,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGpB,6LAAC;gCAAE,WAAU;0CACV,KAAK,OAAO,CAAC,IAAI;;;;;;;;;;;;kCAMxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,KAAK,MAAM,CAAC,MAAM,iBACjB,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK,KAAK,MAAM,CAAC,MAAM;oCACvB,KAAK,KAAK,MAAM,CAAC,IAAI;oCACrB,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;2CAGZ,KAAK,MAAM,CAAC,QAAQ;;;;;;0CAGxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,KAAK,MAAM,CAAC,IAAI;;;;;;kDAEnB,6LAAC;wCAAI,WAAU;kDACZ,KAAK,MAAM,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B;KAzGS;AA2GF,SAAS;IACd,qBACE,6LAAC;QAAQ,IAAG;QAAe,WAAU;;0BAEnC,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAK,WAAU;0CAAqJ;;;;;;0CAIrK,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAIlE,6LAAC;gCAAE,WAAU;0CAA0E;;;;;;;;;;;;kCAMzF,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;gCAAC;gCAC1B,YAAY;oCAAE,UAAU;oCAAI,QAAQ;oCAAU,MAAM;gCAAS;gCAC7D,WAAU;0CAET,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACpC,6LAAC;wCAA4C,MAAM;wCAAM,OAAO;uCAA1C,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;0CAK/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC,CAAC;wCAAI;wCAAG,CAAC;qCAAG;gCAAC;gCAC5B,YAAY;oCAAE,UAAU;oCAAI,QAAQ;oCAAU,MAAM;gCAAS;gCAC7D,WAAU;0CAET,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,sBACrC,6LAAC;wCAAmD,MAAM;wCAAM,OAAO;uCAAjD,GAAG,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO;;;;;;;;;;;;;;;;kCAMxD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDAAwD;;;;;;;;;;;;0CAEzE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDAAwD;;;;;;;;;;;;0CAEzE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDAAwD;;;;;;;;;;;;0CAEzE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;kDAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnF;MAlFgB", "debugId": null}}, {"offset": {"line": 2596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/ContactModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X, Mail, Calendar, MessageSquare, Send, Phone } from 'lucide-react';\nimport { useForm } from 'react-hook-form';\nimport toast from 'react-hot-toast';\nimport { cn } from '@/lib/utils';\n\ninterface ContactModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\ninterface ContactFormData {\n  name: string;\n  email: string;\n  subject: string;\n  message: string;\n}\n\nexport function ContactModal({ isOpen, onClose }: ContactModalProps) {\n  const [activeTab, setActiveTab] = useState<'options' | 'form'>('options');\n  const { register, handleSubmit, formState: { errors }, reset } = useForm<ContactFormData>();\n\n  const handleEmailClick = () => {\n    window.open('mailto:<EMAIL>?subject=Let\\'s Connect!', '_blank');\n    onClose();\n  };\n\n  const handleCallClick = () => {\n    window.open('https://calendly.com/denis-ai-specialist/30min', '_blank');\n    onClose();\n  };\n\n  const handleFormSubmit = async (data: ContactFormData) => {\n    try {\n      // Simulate form submission\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      toast.success('Message sent successfully! I\\'ll get back to you soon.');\n      reset();\n      onClose();\n    } catch (error) {\n      toast.error('Failed to send message. Please try again.');\n    }\n  };\n\n  const contactOptions = [\n    {\n      id: 'email',\n      title: 'Send an Email',\n      description: 'Quick and direct communication',\n      icon: Mail,\n      action: handleEmailClick,\n      color: 'from-blue-500 to-cyan-500',\n    },\n    {\n      id: 'call',\n      title: 'Book a 30 Min Call',\n      description: 'Let\\'s discuss your project in detail',\n      icon: Calendar,\n      action: handleCallClick,\n      color: 'from-green-500 to-emerald-500',\n    },\n    {\n      id: 'form',\n      title: 'Contact Form',\n      description: 'Tell me about your needs',\n      icon: MessageSquare,\n      action: () => setActiveTab('form'),\n      color: 'from-purple-500 to-pink-500',\n    },\n  ];\n\n  if (!isOpen) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4\"\n        onClick={onClose}\n      >\n        <motion.div\n          initial={{ scale: 0.9, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          exit={{ scale: 0.9, opacity: 0 }}\n          transition={{ type: \"spring\", duration: 0.5 }}\n          className=\"bg-white dark:bg-dark-card rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden\"\n          onClick={(e) => e.stopPropagation()}\n        >\n          {/* Header */}\n          <div className=\"relative p-6 border-b border-gray-200 dark:border-gray-700\">\n            <button\n              onClick={onClose}\n              className=\"absolute top-4 right-4 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n            >\n              <X className=\"w-5 h-5 text-gray-500 dark:text-gray-400\" />\n            </button>\n            \n            <div className=\"text-center\">\n              <h2 className=\"text-2xl font-bold text-light-h2 dark:text-dark-h2 mb-2\">\n                We'd Love to Hear From You!\n              </h2>\n              <p className=\"text-light-secondary dark:text-dark-secondary\">\n                {activeTab === 'options' \n                  ? \"Choose how you'd like to connect:\" \n                  : \"Tell us about your project\"\n                }\n              </p>\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-6\">\n            {activeTab === 'options' ? (\n              <div className=\"space-y-4\">\n                {contactOptions.map((option, index) => (\n                  <motion.button\n                    key={option.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    onClick={option.action}\n                    className=\"w-full p-4 rounded-xl border-2 border-gray-200 dark:border-gray-700 hover:border-light-primary dark:hover:border-dark-primary transition-all duration-300 hover:shadow-lg group\"\n                  >\n                    <div className=\"flex items-center space-x-4\">\n                      <div className={cn(\n                        \"w-12 h-12 rounded-lg bg-gradient-to-br flex items-center justify-center\",\n                        option.color\n                      )}>\n                        <option.icon className=\"w-6 h-6 text-white\" />\n                      </div>\n                      <div className=\"flex-1 text-left\">\n                        <h3 className=\"font-semibold text-light-h3 dark:text-dark-h3 group-hover:text-light-primary dark:group-hover:text-dark-primary transition-colors\">\n                          {option.title}\n                        </h3>\n                        <p className=\"text-sm text-light-secondary dark:text-dark-secondary\">\n                          {option.description}\n                        </p>\n                      </div>\n                    </div>\n                  </motion.button>\n                ))}\n              </div>\n            ) : (\n              <motion.form\n                initial={{ opacity: 0, x: 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                onSubmit={handleSubmit(handleFormSubmit)}\n                className=\"space-y-4\"\n              >\n                <button\n                  type=\"button\"\n                  onClick={() => setActiveTab('options')}\n                  className=\"flex items-center text-sm text-light-primary dark:text-dark-primary hover:underline mb-4\"\n                >\n                  ← Back to options\n                </button>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-light-body dark:text-dark-body mb-2\">\n                    Name *\n                  </label>\n                  <input\n                    {...register('name', { required: 'Name is required' })}\n                    className=\"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors\"\n                    placeholder=\"Your full name\"\n                  />\n                  {errors.name && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.name.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-light-body dark:text-dark-body mb-2\">\n                    Email *\n                  </label>\n                  <input\n                    {...register('email', { \n                      required: 'Email is required',\n                      pattern: {\n                        value: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n                        message: 'Please enter a valid email'\n                      }\n                    })}\n                    type=\"email\"\n                    className=\"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                  {errors.email && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.email.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-light-body dark:text-dark-body mb-2\">\n                    Subject *\n                  </label>\n                  <input\n                    {...register('subject', { required: 'Subject is required' })}\n                    className=\"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors\"\n                    placeholder=\"What's this about?\"\n                  />\n                  {errors.subject && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.subject.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-light-body dark:text-dark-body mb-2\">\n                    Message *\n                  </label>\n                  <textarea\n                    {...register('message', { required: 'Message is required' })}\n                    rows={4}\n                    className=\"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors resize-none\"\n                    placeholder=\"Tell me about your project or how I can help...\"\n                  />\n                  {errors.message && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.message.message}</p>\n                  )}\n                </div>\n\n                <button\n                  type=\"submit\"\n                  className=\"w-full flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-medium rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105\"\n                >\n                  <Send className=\"w-4 h-4\" />\n                  <span>Send Message</span>\n                </button>\n              </motion.form>\n            )}\n          </div>\n        </motion.div>\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AAqBO,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAqB;;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/D,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD;IAEvE,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC,oDAAoD;QAChE;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC,kDAAkD;QAC9D;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,2BAA2B;YAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,0JAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA;QACF,EAAE,OAAO,OAAO;YACd,0JAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,qMAAA,CAAA,OAAI;YACV,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,6MAAA,CAAA,WAAQ;YACd,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,2NAAA,CAAA,gBAAa;YACnB,QAAQ,IAAM,aAAa;YAC3B,OAAO;QACT;KACD;IAED,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;sBAET,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAC/B,YAAY;oBAAE,MAAM;oBAAU,UAAU;gBAAI;gBAC5C,WAAU;gBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAGjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0D;;;;;;kDAGxE,6LAAC;wCAAE,WAAU;kDACV,cAAc,YACX,sCACA;;;;;;;;;;;;;;;;;;kCAOV,6LAAC;wBAAI,WAAU;kCACZ,cAAc,0BACb,6LAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;oCACjC,SAAS,OAAO,MAAM;oCACtB,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,2EACA,OAAO,KAAK;0DAEZ,cAAA,6LAAC,OAAO,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,OAAO,KAAK;;;;;;kEAEf,6LAAC;wDAAE,WAAU;kEACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;mCAnBpB,OAAO,EAAE;;;;;;;;;iDA2BpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,UAAU,aAAa;4BACvB,WAAU;;8CAEV,6LAAC;oCACC,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CACX;;;;;;8CAID,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAqE;;;;;;sDAGtF,6LAAC;4CACE,GAAG,SAAS,QAAQ;gDAAE,UAAU;4CAAmB,EAAE;4CACtD,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,IAAI,kBACV,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;8CAIjE,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAqE;;;;;;sDAGtF,6LAAC;4CACE,GAAG,SAAS,SAAS;gDACpB,UAAU;gDACV,SAAS;oDACP,OAAO;oDACP,SAAS;gDACX;4CACF,EAAE;4CACF,MAAK;4CACL,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAIlE,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAqE;;;;;;sDAGtF,6LAAC;4CACE,GAAG,SAAS,WAAW;gDAAE,UAAU;4CAAsB,EAAE;4CAC5D,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,OAAO,kBACb,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;8CAIpE,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAqE;;;;;;sDAGtF,6LAAC;4CACE,GAAG,SAAS,WAAW;gDAAE,UAAU;4CAAsB,EAAE;4CAC5D,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,OAAO,kBACb,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;8CAIpE,6LAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GA5NgB;;QAEmD,iKAAA,CAAA,UAAO;;;KAF1D", "debugId": null}}, {"offset": {"line": 3064, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Header } from '@/components/Header';\nimport { Hero } from '@/components/Hero';\nimport { BlogSection } from '@/components/BlogSection';\nimport { PersonalStory } from '@/components/PersonalStory';\nimport { TestimonialWall } from '@/components/TestimonialWall';\nimport { ContactModal } from '@/components/ContactModal';\n\nexport default function Home() {\n  const [isContactModalOpen, setIsContactModalOpen] = useState(false);\n\n  const handleContactClick = () => {\n    setIsContactModalOpen(true);\n  };\n\n  const handleContactClose = () => {\n    setIsContactModalOpen(false);\n  };\n\n  return (\n    <main className=\"min-h-screen bg-light-background dark:bg-dark-background text-light-body dark:text-dark-body\">\n      <Header onContactClick={handleContactClick} />\n\n      <Hero onContactClick={handleContactClick} />\n\n      <BlogSection />\n\n      <PersonalStory />\n\n      <TestimonialWall />\n\n      {/* Placeholder sections for remaining components */}\n      <section id=\"services\" className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl font-bold gradient-text mb-4\">Services</h2>\n          <p className=\"text-light-secondary dark:text-dark-secondary\">Coming soon...</p>\n        </div>\n      </section>\n\n      <section id=\"portfolio\" className=\"min-h-screen flex items-center justify-center bg-light-card dark:bg-dark-card\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl font-bold gradient-text mb-4\">Portfolio</h2>\n          <p className=\"text-light-secondary dark:text-dark-secondary\">Coming soon...</p>\n        </div>\n      </section>\n\n      <section id=\"faq\" className=\"min-h-screen flex items-center justify-center bg-light-card dark:bg-dark-card\">\n        <div className=\"text-center\">\n          <h2 className=\"text-4xl font-bold gradient-text mb-4\">FAQ</h2>\n          <p className=\"text-light-secondary dark:text-dark-secondary\">Coming soon...</p>\n        </div>\n      </section>\n\n      <ContactModal\n        isOpen={isContactModalOpen}\n        onClose={handleContactClose}\n      />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,qBAAqB;QACzB,sBAAsB;IACxB;IAEA,MAAM,qBAAqB;QACzB,sBAAsB;IACxB;IAEA,qBACE,6LAAC;QAAK,WAAU;;0BACd,6LAAC,+HAAA,CAAA,SAAM;gBAAC,gBAAgB;;;;;;0BAExB,6LAAC,6HAAA,CAAA,OAAI;gBAAC,gBAAgB;;;;;;0BAEtB,6LAAC,oIAAA,CAAA,cAAW;;;;;0BAEZ,6LAAC,sIAAA,CAAA,gBAAa;;;;;0BAEd,6LAAC,wIAAA,CAAA,kBAAe;;;;;0BAGhB,6LAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;;;;;;0BAIjE,6LAAC;gBAAQ,IAAG;gBAAY,WAAU;0BAChC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;;;;;;0BAIjE,6LAAC;gBAAQ,IAAG;gBAAM,WAAU;0BAC1B,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;;;;;;0BAIjE,6LAAC,qIAAA,CAAA,eAAY;gBACX,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAIjB;GAnDwB;KAAA", "debugId": null}}]}