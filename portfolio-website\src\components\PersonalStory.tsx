'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { MapPin, TrendingUp, Users, Award, ArrowRight } from 'lucide-react';

const highlights = [
  {
    icon: MapPin,
    title: 'From Kenya to Global',
    description: 'Started in Nairobi, now serving clients worldwide',
  },
  {
    icon: TrendingUp,
    title: '150+ Projects Delivered',
    description: 'Consistent track record of successful implementations',
  },
  {
    icon: Users,
    title: '50+ Happy Clients',
    description: 'Building lasting relationships through exceptional results',
  },
  {
    icon: Award,
    title: '98% Success Rate',
    description: 'Proven methodology that delivers measurable outcomes',
  },
];

const journeySteps = [
  {
    year: '2019',
    title: 'The Beginning',
    description: 'Started as a curious developer in Nairobi, fascinated by the potential of automation',
  },
  {
    year: '2021',
    title: 'First Breakthrough',
    description: 'Developed my first AI-powered business solution, saving a client 40 hours per week',
  },
  {
    year: '2022',
    title: 'Global Expansion',
    description: '<PERSON>gan working with international clients, scaling solutions across different markets',
  },
  {
    year: '2024',
    title: 'AI Specialist',
    description: 'Now recognized as a leading expert in AI and automation solutions',
  },
];

export function PersonalStory() {
  return (
    <section id="story" className="py-20 bg-light-card dark:bg-dark-card">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Side - Image/Visual */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            {/* Main Image */}
            <div className="relative">
              <div className="relative w-full h-96 rounded-2xl overflow-hidden shadow-2xl">
                <Image
                  src="/placeholder.png"
                  alt="Denis working on AI solutions"
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />
              </div>
              
              {/* Floating Stats */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
                className="absolute -top-6 -right-6 bg-white dark:bg-dark-background rounded-2xl p-6 shadow-xl border border-light-primary/20 dark:border-dark-primary/20"
              >
                <div className="text-center">
                  <div className="text-3xl font-bold gradient-text">5+</div>
                  <div className="text-sm text-light-secondary dark:text-dark-secondary">Years</div>
                  <div className="text-sm text-light-secondary dark:text-dark-secondary">Experience</div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                viewport={{ once: true }}
                className="absolute -bottom-6 -left-6 bg-white dark:bg-dark-background rounded-2xl p-6 shadow-xl border border-light-primary/20 dark:border-dark-primary/20"
              >
                <div className="text-center">
                  <div className="text-3xl font-bold gradient-text">98%</div>
                  <div className="text-sm text-light-secondary dark:text-dark-secondary">Success</div>
                  <div className="text-sm text-light-secondary dark:text-dark-secondary">Rate</div>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Right Side - Story Content */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Header */}
            <div>
              <span className="inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4">
                🚀 My Journey
              </span>
              
              <h2 className="text-4xl sm:text-5xl font-bold mb-6">
                <span className="text-light-h2 dark:text-dark-h2">The Journey to</span>
                <br />
                <span className="gradient-text">AI Specialist</span>
              </h2>
              
              <h3 className="text-2xl font-semibold text-light-h3 dark:text-dark-h3 mb-6">
                Turning Challenges Into Opportunities
              </h3>
            </div>

            {/* Story Content */}
            <div className="space-y-6">
              <p className="text-lg text-light-body dark:text-dark-body leading-relaxed">
                My journey began in Nairobi, Kenya, where I discovered the transformative power of 
                technology. What started as curiosity about automation quickly evolved into a passion 
                for solving complex business challenges through AI and intelligent systems.
              </p>
              
              <p className="text-lg text-light-body dark:text-dark-body leading-relaxed">
                From those early days of experimenting with simple scripts to now architecting 
                enterprise-level AI solutions, every project has been a stepping stone toward 
                mastering the art of digital transformation.
              </p>
              
              <p className="text-lg text-light-body dark:text-dark-body leading-relaxed">
                Today, I help businesses worldwide unlock their potential through cutting-edge 
                automation strategies, proving that with the right approach, any challenge can 
                become an opportunity for growth.
              </p>
            </div>

            {/* Highlights Grid */}
            <div className="grid grid-cols-2 gap-4 mt-8">
              {highlights.map((highlight, index) => (
                <motion.div
                  key={highlight.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
                  viewport={{ once: true }}
                  className="p-4 bg-white dark:bg-dark-background rounded-xl border border-light-primary/10 dark:border-dark-primary/10"
                >
                  <highlight.icon className="w-6 h-6 text-light-primary dark:text-dark-primary mb-2" />
                  <h4 className="font-semibold text-light-h3 dark:text-dark-h3 text-sm mb-1">
                    {highlight.title}
                  </h4>
                  <p className="text-xs text-light-secondary dark:text-dark-secondary">
                    {highlight.description}
                  </p>
                </motion.div>
              ))}
            </div>

            {/* CTA */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              viewport={{ once: true }}
              className="pt-6"
            >
              <button className="group inline-flex items-center px-8 py-4 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-semibold rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105">
                Let's Build Something Amazing
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </button>
            </motion.div>
          </motion.div>
        </div>

        {/* Journey Timeline */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-20"
        >
          <h3 className="text-3xl font-bold text-center gradient-text mb-12">
            The Journey Timeline
          </h3>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {journeySteps.map((step, index) => (
              <motion.div
                key={step.year}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="relative">
                  <div className="w-16 h-16 mx-auto bg-gradient-primary-button dark:bg-gradient-primary-button rounded-full flex items-center justify-center text-white font-bold text-lg mb-4">
                    {step.year}
                  </div>
                  {index < journeySteps.length - 1 && (
                    <div className="hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-light-primary to-light-primary/20 dark:from-dark-primary dark:to-dark-primary/20" />
                  )}
                </div>
                <h4 className="font-bold text-light-h3 dark:text-dark-h3 mb-2">
                  {step.title}
                </h4>
                <p className="text-sm text-light-secondary dark:text-dark-secondary">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
