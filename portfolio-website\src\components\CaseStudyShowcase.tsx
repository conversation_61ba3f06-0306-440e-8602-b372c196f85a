'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { ArrowRight, TrendingUp, Clock, Target, BarChart3 } from 'lucide-react';
import type { CaseStudy } from '@/types';

const featuredCaseStudy: CaseStudy = {
  id: '1',
  title: 'Content & Marketing Analytics Transformation',
  description: 'Revolutionized a digital marketing agency\'s analytics workflow with AI-powered automation, delivering unprecedented insights and efficiency gains.',
  metrics: [
    {
      label: 'Audience Reach Growth',
      value: '120%',
      improvement: '+85% vs previous quarter'
    },
    {
      label: 'Project Timeline',
      value: '2 Months',
      improvement: '50% faster than estimated'
    },
    {
      label: 'Monthly Engagement',
      value: '60%+',
      improvement: 'Sustained growth rate'
    },
    {
      label: 'Cost Reduction',
      value: '40%',
      improvement: 'In manual analysis time'
    }
  ],
  timeline: '2 months',
  image: '/placeholder.png',
  slug: 'content-marketing-analytics-transformation'
};

const additionalCaseStudies = [
  {
    id: '2',
    title: 'E-commerce Automation Suite',
    description: 'Streamlined inventory management and customer service for a growing e-commerce platform.',
    metrics: '300% efficiency increase',
    image: '/placeholder.png'
  },
  {
    id: '3',
    title: 'Healthcare Data Processing',
    description: 'Automated patient data analysis and reporting for a healthcare network.',
    metrics: '80% time savings',
    image: '/placeholder.png'
  },
  {
    id: '4',
    title: 'Financial Risk Assessment',
    description: 'AI-powered risk analysis system for a fintech startup.',
    metrics: '95% accuracy rate',
    image: '/placeholder.png'
  }
];

const technologies = [
  { name: 'Python', icon: '🐍' },
  { name: 'TensorFlow', icon: '🧠' },
  { name: 'Google Analytics', icon: '📊' },
  { name: 'Power BI', icon: '📈' },
  { name: 'AWS', icon: '☁️' },
  { name: 'Docker', icon: '🐳' }
];

export function CaseStudyShowcase() {
  return (
    <section id="case-studies" className="py-20 bg-light-card dark:bg-dark-card">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <span className="inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4">
            🎯 Success Stories
          </span>
          
          <h2 className="text-4xl sm:text-5xl font-bold gradient-text mb-6">
            Transformative Results
          </h2>
          
          <p className="text-lg text-light-secondary dark:text-dark-secondary max-w-3xl mx-auto">
            Real projects, real impact. See how AI and automation have transformed businesses 
            across different industries.
          </p>
        </motion.div>

        {/* Featured Case Study */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="bg-white dark:bg-dark-background rounded-3xl overflow-hidden shadow-2xl mb-16"
        >
          <div className="grid lg:grid-cols-2 gap-0">
            {/* Left Side - Content */}
            <div className="p-8 lg:p-12">
              <div className="mb-6">
                <div className="flex items-center mb-4">
                  <Target className="w-6 h-6 text-light-primary dark:text-dark-primary mr-2" />
                  <span className="text-sm font-medium text-light-primary dark:text-dark-primary">
                    Featured Case Study
                  </span>
                </div>
                
                <h3 className="text-3xl font-bold text-light-h2 dark:text-dark-h2 mb-4">
                  {featuredCaseStudy.title}
                </h3>
                
                <p className="text-lg text-light-body dark:text-dark-body leading-relaxed mb-6">
                  {featuredCaseStudy.description}
                </p>
              </div>

              {/* Metrics Grid */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                {featuredCaseStudy.metrics.map((metric, index) => (
                  <motion.div
                    key={metric.label}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="p-4 bg-light-background dark:bg-dark-card rounded-xl border border-light-primary/10 dark:border-dark-primary/10"
                  >
                    <div className="text-2xl font-bold gradient-text mb-1">
                      {metric.value}
                    </div>
                    <div className="text-sm font-medium text-light-h3 dark:text-dark-h3 mb-1">
                      {metric.label}
                    </div>
                    <div className="text-xs text-light-secondary dark:text-dark-secondary">
                      {metric.improvement}
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Technologies Used */}
              <div className="mb-8">
                <h4 className="text-sm font-medium text-light-secondary dark:text-dark-secondary mb-3">
                  Technologies Used:
                </h4>
                <div className="flex flex-wrap gap-2">
                  {technologies.map((tech) => (
                    <span
                      key={tech.name}
                      className="inline-flex items-center px-3 py-1 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium"
                    >
                      <span className="mr-1">{tech.icon}</span>
                      {tech.name}
                    </span>
                  ))}
                </div>
              </div>

              {/* CTA */}
              <button className="group inline-flex items-center px-8 py-4 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-semibold rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105">
                Read Full Case Study
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </button>
            </div>

            {/* Right Side - Analytics Dashboard Image */}
            <div className="relative lg:h-auto h-64">
              <Image
                src={featuredCaseStudy.image}
                alt="Analytics Dashboard"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
              
              {/* Floating Metrics */}
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                viewport={{ once: true }}
                className="absolute top-6 right-6 bg-white dark:bg-dark-background rounded-xl p-4 shadow-lg"
              >
                <div className="flex items-center mb-2">
                  <TrendingUp className="w-5 h-5 text-green-500 mr-2" />
                  <span className="text-sm font-medium text-light-h3 dark:text-dark-h3">
                    Live Metrics
                  </span>
                </div>
                <div className="text-2xl font-bold gradient-text">120%</div>
                <div className="text-xs text-light-secondary dark:text-dark-secondary">
                  Growth Rate
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                viewport={{ once: true }}
                className="absolute bottom-6 right-6 bg-white dark:bg-dark-background rounded-xl p-4 shadow-lg"
              >
                <div className="flex items-center mb-2">
                  <Clock className="w-5 h-5 text-blue-500 mr-2" />
                  <span className="text-sm font-medium text-light-h3 dark:text-dark-h3">
                    Timeline
                  </span>
                </div>
                <div className="text-2xl font-bold gradient-text">2 Mo</div>
                <div className="text-xs text-light-secondary dark:text-dark-secondary">
                  Completion
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Additional Case Studies */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold text-center gradient-text mb-8">
            More Success Stories
          </h3>
          
          <div className="grid md:grid-cols-3 gap-6">
            {additionalCaseStudies.map((study, index) => (
              <motion.div
                key={study.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group bg-white dark:bg-dark-background rounded-2xl overflow-hidden shadow-card hover:shadow-card-hover transition-all duration-300 hover:-translate-y-2"
              >
                <div className="relative h-48">
                  <Image
                    src={study.image}
                    alt={study.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="text-white font-bold text-lg mb-1">
                      {study.metrics}
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  <h4 className="font-bold text-light-h3 dark:text-dark-h3 mb-2 group-hover:text-light-primary dark:group-hover:text-dark-primary transition-colors">
                    {study.title}
                  </h4>
                  <p className="text-sm text-light-secondary dark:text-dark-secondary line-clamp-2">
                    {study.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-16 pt-16 border-t border-light-primary/20 dark:border-dark-primary/20"
        >
          <h3 className="text-2xl font-bold text-light-h2 dark:text-dark-h2 mb-4">
            Ready to Transform Your Business?
          </h3>
          <p className="text-light-secondary dark:text-dark-secondary mb-8 max-w-2xl mx-auto">
            Let's discuss how AI and automation can drive similar results for your organization.
          </p>
          <button className="group inline-flex items-center px-8 py-4 bg-light-card dark:bg-dark-card border-2 border-light-primary dark:border-dark-primary text-light-primary dark:text-dark-primary font-semibold rounded-lg hover:bg-light-primary dark:hover:bg-dark-primary hover:text-white transition-all duration-300 hover:scale-105">
            View All Case Studies
            <BarChart3 className="ml-2 w-5 h-5" />
          </button>
        </motion.div>
      </div>
    </section>
  );
}
