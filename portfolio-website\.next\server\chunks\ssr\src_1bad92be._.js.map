{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\nimport { ArrowRight, Play } from 'lucide-react';\n\ninterface HeroProps {\n  onContactClick: () => void;\n}\n\nexport function Hero({ onContactClick }: HeroProps) {\n  const scrollToPortfolio = () => {\n    const element = document.querySelector('#portfolio');\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section id=\"home\" className=\"pt-16 pb-20 bg-gradient-to-br from-blue-50 via-white to-cyan-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid lg:grid-cols-2 gap-12 lg:gap-16 items-center min-h-[80vh]\">\n          {/* Left Content */}\n          <div className=\"text-center lg:text-left\">\n            <div className=\"mb-8\">\n              <div className=\"inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6\">\n                <span className=\"w-2 h-2 bg-blue-600 rounded-full mr-2\"></span>\n                AI & Automation Specialist\n              </div>\n\n              <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6\">\n                Transform Your Business with\n                <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600\"> AI-Powered </span>\n                Solutions\n              </h1>\n\n              <p className=\"text-xl text-gray-600 max-w-2xl mx-auto lg:mx-0 leading-relaxed mb-8\">\n                From Kenya to global success, I help businesses unlock their potential through\n                cutting-edge AI solutions and automation strategies that deliver measurable results.\n              </p>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-3 gap-8 mb-10\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-gray-900 mb-1\">150+</div>\n                <p className=\"text-sm text-gray-600\">Projects Completed</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-gray-900 mb-1\">50+</div>\n                <p className=\"text-sm text-gray-600\">Happy Clients</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-gray-900 mb-1\">98%</div>\n                <p className=\"text-sm text-gray-600\">Success Rate</p>\n              </div>\n            </div>\n\n            {/* CTAs */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\">\n              <button\n                onClick={scrollToPortfolio}\n                className=\"inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200\"\n              >\n                View Case Studies\n                <ArrowRight className=\"ml-2 w-5 h-5\" />\n              </button>\n\n              <button\n                onClick={onContactClick}\n                className=\"inline-flex items-center px-8 py-4 border-2 border-blue-600 text-blue-600 font-semibold rounded-lg hover:bg-blue-600 hover:text-white transition-all duration-200\"\n              >\n                <Play className=\"mr-2 w-5 h-5\" />\n                Book a Call\n              </button>\n            </div>\n          </div>\n\n          {/* Right Content - Professional Image */}\n          <div className=\"relative\">\n            <div className=\"relative w-full max-w-lg mx-auto\">\n              {/* Main Image */}\n              <div className=\"relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-blue-100 to-cyan-100\">\n                <Image\n                  src=\"/Denis-Personal-picture.png\"\n                  alt=\"Denis - AI & Automation Specialist\"\n                  fill\n                  className=\"object-cover\"\n                  priority\n                />\n              </div>\n\n              {/* Floating Elements */}\n              <div className=\"absolute -top-4 -right-4 w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg\">\n                AI\n              </div>\n\n              <div className=\"absolute -bottom-4 -left-4 w-16 h-16 bg-cyan-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg\">\n                5+\n              </div>\n\n              <div className=\"absolute top-1/2 -left-6 w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg\">\n                ML\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <div className=\"text-center mt-16\">\n          <div className=\"inline-flex items-center text-gray-500 text-sm\">\n            <div className=\"w-6 h-10 border-2 border-gray-300 rounded-full flex justify-center mr-3\">\n              <div className=\"w-1 h-3 bg-gray-400 rounded-full mt-2 animate-bounce\"></div>\n            </div>\n            Scroll to explore\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AASO,SAAS,KAAK,EAAE,cAAc,EAAa;IAChD,MAAM,oBAAoB;QACxB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;;;;;gDAA+C;;;;;;;sDAIjE,8OAAC;4CAAG,WAAU;;gDAA8E;8DAE1F,8OAAC;oDAAK,WAAU;8DAA2E;;;;;;gDAAmB;;;;;;;sDAIhH,8OAAC;4CAAE,WAAU;sDAAuE;;;;;;;;;;;;8CAOtF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAKzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;;gDACX;8DAEC,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAGxB,8OAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;4CACV,QAAQ;;;;;;;;;;;kDAKZ,8OAAC;wCAAI,WAAU;kDAAsI;;;;;;kDAIrJ,8OAAC;wCAAI,WAAU;kDAAgI;;;;;;kDAI/I,8OAAC;wCAAI,WAAU;kDAAwI;;;;;;;;;;;;;;;;;;;;;;;8BAQ7J,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;4BACX;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/ContactModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { X, Mail, Calendar, MessageSquare, Send } from 'lucide-react';\n\ninterface ContactModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport function ContactModal({ isOpen, onClose }: ContactModalProps) {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4\" onClick={onClose}>\n      <div\n        className=\"bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden\"\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Header */}\n        <div className=\"relative p-6 border-b border-gray-200\">\n          <button\n            onClick={onClose}\n            className=\"absolute top-4 right-4 p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n          >\n            <X className=\"w-5 h-5 text-gray-500\" />\n          </button>\n\n          <div className=\"text-center\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n              We'd Love to Hear From You!\n            </h2>\n            <p className=\"text-gray-600\">\n              Choose how you'd like to connect:\n            </p>\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6\">\n          <div className=\"space-y-4\">\n            <button\n              onClick={() => {\n                window.open('mailto:<EMAIL>?subject=Let\\'s Connect!', '_blank');\n                onClose();\n              }}\n              className=\"w-full p-4 rounded-xl border-2 border-gray-200 hover:border-blue-500 transition-all duration-300 hover:shadow-lg group\"\n            >\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center\">\n                  <Mail className=\"w-6 h-6 text-white\" />\n                </div>\n                <div className=\"flex-1 text-left\">\n                  <h3 className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\">\n                    Send an Email\n                  </h3>\n                  <p className=\"text-sm text-gray-600\">\n                    Quick and direct communication\n                  </p>\n                </div>\n              </div>\n            </button>\n\n            <button\n              onClick={() => {\n                window.open('https://calendly.com/denis-ai-specialist/30min', '_blank');\n                onClose();\n              }}\n              className=\"w-full p-4 rounded-xl border-2 border-gray-200 hover:border-blue-500 transition-all duration-300 hover:shadow-lg group\"\n            >\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 rounded-lg bg-gradient-to-br from-green-500 to-emerald-500 flex items-center justify-center\">\n                  <Calendar className=\"w-6 h-6 text-white\" />\n                </div>\n                <div className=\"flex-1 text-left\">\n                  <h3 className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\">\n                    Book a 30 Min Call\n                  </h3>\n                  <p className=\"text-sm text-gray-600\">\n                    Let's discuss your project in detail\n                  </p>\n                </div>\n              </div>\n            </button>\n\n            <button\n              onClick={() => {\n                // For now, just show an alert - can implement form later\n                alert('Contact form coming soon! Please use email or calendar for now.');\n              }}\n              className=\"w-full p-4 rounded-xl border-2 border-gray-200 hover:border-blue-500 transition-all duration-300 hover:shadow-lg group\"\n            >\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 rounded-lg bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center\">\n                  <MessageSquare className=\"w-6 h-6 text-white\" />\n                </div>\n                <div className=\"flex-1 text-left\">\n                  <h3 className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\">\n                    Contact Form\n                  </h3>\n                  <p className=\"text-sm text-gray-600\">\n                    Tell me about your needs\n                  </p>\n                </div>\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAHA;;;AAUO,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAqB;IACjE,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;QAAuF,SAAS;kBAC7G,cAAA,8OAAC;YACC,WAAU;YACV,SAAS,CAAC,IAAM,EAAE,eAAe;;8BAGjC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAGf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;8BAOjC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;oCACP,OAAO,IAAI,CAAC,oDAAoD;oCAChE;gCACF;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA0E;;;;;;8DAGxF,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;0CAO3C,8OAAC;gCACC,SAAS;oCACP,OAAO,IAAI,CAAC,kDAAkD;oCAC9D;gCACF;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA0E;;;;;;8DAGxF,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;0CAO3C,8OAAC;gCACC,SAAS;oCACP,yDAAyD;oCACzD,MAAM;gCACR;gCACA,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA0E;;;;;;8DAGxF,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvD", "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Header } from '@/components/Header';\nimport { Hero } from '@/components/Hero';\nimport { BlogSection } from '@/components/BlogSection';\nimport { PersonalStory } from '@/components/PersonalStory';\nimport { CaseStudyShowcase } from '@/components/CaseStudyShowcase';\nimport { TestimonialWall } from '@/components/TestimonialWall';\nimport { FAQ } from '@/components/FAQ';\nimport { Footer } from '@/components/Footer';\nimport { ContactModal } from '@/components/ContactModal';\n\nexport default function Home() {\n  const [isContactModalOpen, setIsContactModalOpen] = useState(false);\n\n  const handleContactClick = () => {\n    setIsContactModalOpen(true);\n  };\n\n  const handleContactClose = () => {\n    setIsContactModalOpen(false);\n  };\n\n  return (\n    <main className=\"min-h-screen bg-white text-gray-900\">\n      <Header onContactClick={handleContactClick} />\n\n      <Hero onContactClick={handleContactClick} />\n\n      {/* Temporary placeholder sections - will rebuild to match samples */}\n      <section id=\"about\" className=\"py-20 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">About Section</h2>\n          <p className=\"text-gray-600\">Rebuilding to match sample design...</p>\n        </div>\n      </section>\n\n      <section id=\"services\" className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Services Section</h2>\n          <p className=\"text-gray-600\">Rebuilding to match sample design...</p>\n        </div>\n      </section>\n\n      <section id=\"portfolio\" className=\"py-20 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Portfolio Section</h2>\n          <p className=\"text-gray-600\">Rebuilding to match sample design...</p>\n        </div>\n      </section>\n\n      <section id=\"testimonials\" className=\"py-20 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Testimonials Section</h2>\n          <p className=\"text-gray-600\">Rebuilding to match sample design...</p>\n        </div>\n      </section>\n\n      <section id=\"contact\" className=\"py-20 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Contact Section</h2>\n          <p className=\"text-gray-600\">Rebuilding to match sample design...</p>\n        </div>\n      </section>\n\n      <ContactModal\n        isOpen={isContactModalOpen}\n        onClose={handleContactClose}\n      />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAOA;AAXA;;;;;;AAae,SAAS;IACtB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,qBAAqB;QACzB,sBAAsB;IACxB;IAEA,MAAM,qBAAqB;QACzB,sBAAsB;IACxB;IAEA,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC,4HAAA,CAAA,SAAM;gBAAC,gBAAgB;;;;;;0BAExB,8OAAC,0HAAA,CAAA,OAAI;gBAAC,gBAAgB;;;;;;0BAGtB,8OAAC;gBAAQ,IAAG;gBAAQ,WAAU;0BAC5B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAIjC,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAIjC,8OAAC;gBAAQ,IAAG;gBAAY,WAAU;0BAChC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAIjC,8OAAC;gBAAQ,IAAG;gBAAe,WAAU;0BACnC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAIjC,8OAAC;gBAAQ,IAAG;gBAAU,WAAU;0BAC9B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAIjC,8OAAC,kIAAA,CAAA,eAAY;gBACX,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}