'use client';

import { useState } from 'react';
import { Header } from '@/components/Header';
import { Hero } from '@/components/Hero';
import { BlogSection } from '@/components/BlogSection';
import { PersonalStory } from '@/components/PersonalStory';
import { CaseStudyShowcase } from '@/components/CaseStudyShowcase';
import { TestimonialWall } from '@/components/TestimonialWall';
import { FAQ } from '@/components/FAQ';
import { Footer } from '@/components/Footer';
import { ContactModal } from '@/components/ContactModal';

export default function Home() {
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);

  const handleContactClick = () => {
    setIsContactModalOpen(true);
  };

  const handleContactClose = () => {
    setIsContactModalOpen(false);
  };

  return (
    <main className="min-h-screen bg-white text-gray-900">
      <Header onContactClick={handleContactClick} />

      <Hero onContactClick={handleContactClick} />

      {/* Temporary placeholder sections - will rebuild to match samples */}
      <section id="about" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">About Section</h2>
          <p className="text-gray-600">Rebuilding to match sample design...</p>
        </div>
      </section>

      <section id="services" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Services Section</h2>
          <p className="text-gray-600">Rebuilding to match sample design...</p>
        </div>
      </section>

      <section id="portfolio" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Portfolio Section</h2>
          <p className="text-gray-600">Rebuilding to match sample design...</p>
        </div>
      </section>

      <section id="testimonials" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Testimonials Section</h2>
          <p className="text-gray-600">Rebuilding to match sample design...</p>
        </div>
      </section>

      <section id="contact" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Contact Section</h2>
          <p className="text-gray-600">Rebuilding to match sample design...</p>
        </div>
      </section>

      <ContactModal
        isOpen={isContactModalOpen}
        onClose={handleContactClose}
      />
    </main>
  );
}
