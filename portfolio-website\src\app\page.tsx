'use client';

import { useState } from 'react';
import { Header } from '@/components/Header';
import { Hero } from '@/components/Hero';
import { BlogSection } from '@/components/BlogSection';
import { PersonalStory } from '@/components/PersonalStory';
import { TestimonialWall } from '@/components/TestimonialWall';
import { ContactModal } from '@/components/ContactModal';

export default function Home() {
  const [isContactModalOpen, setIsContactModalOpen] = useState(false);

  const handleContactClick = () => {
    setIsContactModalOpen(true);
  };

  const handleContactClose = () => {
    setIsContactModalOpen(false);
  };

  return (
    <main className="min-h-screen bg-light-background dark:bg-dark-background text-light-body dark:text-dark-body">
      <Header onContactClick={handleContactClick} />

      <Hero onContactClick={handleContactClick} />

      <BlogSection />

      <PersonalStory />

      <TestimonialWall />

      {/* Placeholder sections for remaining components */}
      <section id="services" className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-4xl font-bold gradient-text mb-4">Services</h2>
          <p className="text-light-secondary dark:text-dark-secondary">Coming soon...</p>
        </div>
      </section>

      <section id="portfolio" className="min-h-screen flex items-center justify-center bg-light-card dark:bg-dark-card">
        <div className="text-center">
          <h2 className="text-4xl font-bold gradient-text mb-4">Portfolio</h2>
          <p className="text-light-secondary dark:text-dark-secondary">Coming soon...</p>
        </div>
      </section>

      <section id="faq" className="min-h-screen flex items-center justify-center bg-light-card dark:bg-dark-card">
        <div className="text-center">
          <h2 className="text-4xl font-bold gradient-text mb-4">FAQ</h2>
          <p className="text-light-secondary dark:text-dark-secondary">Coming soon...</p>
        </div>
      </section>

      <ContactModal
        isOpen={isContactModalOpen}
        onClose={handleContactClose}
      />
    </main>
  );
}
