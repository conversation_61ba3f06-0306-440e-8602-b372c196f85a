'use client';

import { motion } from 'framer-motion';
import { Star, Play, TrendingUp, Users } from 'lucide-react';
import Image from 'next/image';
import type { TestimonialCard } from '@/types';

const testimonialCards: TestimonialCard[] = [
  {
    id: '1',
    type: 'text',
    size: 'medium',
    content: {
      text: "<PERSON> transformed our entire workflow with AI automation. We're now processing 3x more data with half the manual effort!"
    },
    author: {
      name: '<PERSON>',
      role: 'Operations Director',
      avatar: '/placeholder.png',
      initials: 'SJ'
    }
  },
  {
    id: '2',
    type: 'stat',
    size: 'small',
    accent: 'gold',
    content: {
      stat: '300%',
      metric: 'Efficiency Increase'
    },
    author: {
      name: 'TechCorp',
      role: 'Client Result',
      initials: 'TC'
    }
  },
  {
    id: '3',
    type: 'video',
    size: 'large',
    content: {
      text: "Watch how Denis helped us automate our customer service and reduce response time by 80%"
    },
    author: {
      name: '<PERSON>',
      role: 'CEO, InnovateLab',
      avatar: '/placeholder.png',
      initials: 'MC'
    },
    video: {
      thumbnail: '/placeholder.png'
    }
  },
  {
    id: '4',
    type: 'quote',
    size: 'medium',
    content: {
      quote: "The ROI from <PERSON>'s AI solutions exceeded our expectations by 250%. Absolutely game-changing!"
    },
    author: {
      name: 'Emma Rodriguez',
      role: 'CFO, DataFlow Inc',
      avatar: '/placeholder.png',
      initials: 'ER'
    }
  },
  {
    id: '5',
    type: 'stat',
    size: 'small',
    accent: 'green',
    content: {
      stat: '40hrs',
      metric: 'Saved Weekly'
    },
    author: {
      name: 'StartupXYZ',
      role: 'Client Result',
      initials: 'SX'
    }
  },
  {
    id: '6',
    type: 'text',
    size: 'medium',
    content: {
      text: "Professional, innovative, and results-driven. Denis doesn't just deliver solutions, he delivers transformation."
    },
    author: {
      name: 'David Park',
      role: 'CTO, FutureTech',
      avatar: '/placeholder.png',
      initials: 'DP'
    }
  },
  {
    id: '7',
    type: 'stat',
    size: 'small',
    accent: 'gold',
    content: {
      stat: '98%',
      metric: 'Client Satisfaction'
    },
    author: {
      name: 'Overall Rating',
      role: 'Client Feedback',
      initials: 'OR'
    }
  },
  {
    id: '8',
    type: 'text',
    size: 'large',
    content: {
      text: "From Kenya to global impact - Denis brings world-class AI expertise with a personal touch that makes all the difference."
    },
    author: {
      name: 'Lisa Thompson',
      role: 'VP Innovation, GlobalCorp',
      avatar: '/placeholder.png',
      initials: 'LT'
    }
  }
];

// Duplicate cards for infinite scroll effect
const infiniteCards = [...testimonialCards, ...testimonialCards, ...testimonialCards];

function TestimonialCard({ card, index }: { card: TestimonialCard; index: number }) {
  const sizeClasses = {
    small: 'h-32',
    medium: 'h-40',
    large: 'h-48'
  };

  const accentClasses = {
    gold: 'border-yellow-400 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20',
    green: 'border-green-400 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20'
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      viewport={{ once: true }}
      className={`
        ${sizeClasses[card.size]} 
        ${card.accent ? accentClasses[card.accent] : 'bg-white dark:bg-dark-card border-light-primary/20 dark:border-dark-primary/20'}
        rounded-2xl p-4 border-2 shadow-card hover:shadow-card-hover transition-all duration-300 hover:-translate-y-1 relative overflow-hidden
      `}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 right-0 w-20 h-20 bg-light-primary dark:bg-dark-primary rounded-full -translate-y-10 translate-x-10" />
      </div>

      <div className="relative h-full flex flex-col">
        {/* Content based on type */}
        {card.type === 'stat' && (
          <div className="flex-1 flex flex-col justify-center text-center">
            <div className="text-3xl font-bold gradient-text mb-1">
              {card.content.stat}
            </div>
            <div className="text-sm text-light-secondary dark:text-dark-secondary">
              {card.content.metric}
            </div>
          </div>
        )}

        {card.type === 'text' && (
          <div className="flex-1">
            <p className="text-sm text-light-body dark:text-dark-body leading-relaxed line-clamp-4">
              "{card.content.text}"
            </p>
          </div>
        )}

        {card.type === 'quote' && (
          <div className="flex-1">
            <div className="text-2xl text-light-primary dark:text-dark-primary mb-2">"</div>
            <p className="text-sm text-light-body dark:text-dark-body leading-relaxed line-clamp-3">
              {card.content.quote}
            </p>
          </div>
        )}

        {card.type === 'video' && (
          <div className="flex-1 relative">
            <div className="relative h-20 rounded-lg overflow-hidden mb-2">
              <Image
                src={card.video?.thumbnail || '/placeholder.png'}
                alt="Video thumbnail"
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                <Play className="w-6 h-6 text-white" />
              </div>
            </div>
            <p className="text-xs text-light-secondary dark:text-dark-secondary line-clamp-2">
              {card.content.text}
            </p>
          </div>
        )}

        {/* Author */}
        <div className="flex items-center mt-3 pt-3 border-t border-light-primary/10 dark:border-dark-primary/10">
          <div className="w-8 h-8 rounded-full bg-light-primary dark:bg-dark-primary flex items-center justify-center text-white text-xs font-medium mr-3">
            {card.author.avatar ? (
              <Image
                src={card.author.avatar}
                alt={card.author.name}
                width={32}
                height={32}
                className="rounded-full"
              />
            ) : (
              card.author.initials
            )}
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-xs font-medium text-light-h3 dark:text-dark-h3 truncate">
              {card.author.name}
            </div>
            <div className="text-xs text-light-secondary dark:text-dark-secondary truncate">
              {card.author.role}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

export function TestimonialWall() {
  return (
    <section id="testimonials" className="py-20 bg-light-background dark:bg-dark-background relative overflow-hidden">
      {/* Fade overlays */}
      <div className="absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-light-background dark:from-dark-background to-transparent z-10" />
      <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-light-background dark:from-dark-background to-transparent z-10" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <span className="inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4">
            💬 Client Love
          </span>
          
          <h2 className="text-4xl sm:text-5xl font-bold gradient-text mb-6">
            What People Are Saying
          </h2>
          
          <p className="text-lg text-light-secondary dark:text-dark-secondary max-w-3xl mx-auto">
            Real feedback from real clients who've experienced the transformative power of AI automation.
          </p>
        </motion.div>

        {/* Testimonial Mosaic */}
        <div className="relative">
          {/* Left sliding column */}
          <motion.div
            animate={{ y: [0, -50, 0] }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          >
            {infiniteCards.slice(0, 8).map((card, index) => (
              <TestimonialCard key={`${card.id}-${index}`} card={card} index={index} />
            ))}
          </motion.div>

          {/* Right sliding column (offset) */}
          <motion.div
            animate={{ y: [-50, 0, -50] }}
            transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6"
          >
            {infiniteCards.slice(4, 12).map((card, index) => (
              <TestimonialCard key={`${card.id}-offset-${index}`} card={card} index={index} />
            ))}
          </motion.div>
        </div>

        {/* Stats Summary */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
          className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 pt-16 border-t border-light-primary/20 dark:border-dark-primary/20"
        >
          <div className="text-center">
            <div className="text-3xl font-bold gradient-text mb-2">150+</div>
            <div className="text-sm text-light-secondary dark:text-dark-secondary">Projects Completed</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold gradient-text mb-2">50+</div>
            <div className="text-sm text-light-secondary dark:text-dark-secondary">Happy Clients</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold gradient-text mb-2">98%</div>
            <div className="text-sm text-light-secondary dark:text-dark-secondary">Success Rate</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold gradient-text mb-2">5+</div>
            <div className="text-sm text-light-secondary dark:text-dark-secondary">Years Experience</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
