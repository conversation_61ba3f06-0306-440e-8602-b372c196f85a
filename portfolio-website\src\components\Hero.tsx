'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';
import { ArrowRight, Play, Star, Users, TrendingUp } from 'lucide-react';

interface HeroProps {
  onContactClick: () => void;
}

const platformIcons = [
  { name: 'WordPress', icon: '🔧' },
  { name: 'Google Analytics', icon: '📊' },
  { name: 'OpenAI', icon: '🤖' },
  { name: 'Automation', icon: '⚡' },
  { name: 'Data Science', icon: '📈' },
];

const stats = [
  { label: 'Projects Completed', value: '150+', icon: TrendingUp },
  { label: 'Happy Clients', value: '50+', icon: Users },
  { label: 'Success Rate', value: '98%', icon: Star },
];

export function Hero({ onContactClick }: HeroProps) {
  const scrollToPortfolio = () => {
    const element = document.querySelector('#portfolio');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden bg-light-background dark:bg-dark-background">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-light-primary/5 via-transparent to-light-primary/10 dark:from-dark-primary/5 dark:to-dark-primary/10" />
      
      {/* Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-light-primary/20 dark:bg-dark-primary/20 rounded-full"
            animate={{
              y: [0, -100, 0],
              x: [0, 50, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 6 + i,
              repeat: Infinity,
              delay: i * 0.5,
            }}
            style={{
              left: `${10 + i * 15}%`,
              top: `${20 + i * 10}%`,
            }}
          />
        ))}
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Left Content */}
          <div className="text-center lg:text-left">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-6"
            >
              <span className="inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4">
                🚀 AI & Automation Specialist
              </span>
              
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                <span className="gradient-text">Transform Your Business</span>
                <br />
                <span className="text-light-h2 dark:text-dark-h2">
                  with Intelligent
                </span>
                <br />
                <span className="gradient-text">Automation</span>
              </h1>
              
              <p className="text-lg sm:text-xl text-light-secondary dark:text-dark-secondary max-w-2xl mx-auto lg:mx-0 leading-relaxed">
                From Kenya to global success, I help businesses unlock their potential through 
                cutting-edge AI solutions and automation strategies that deliver measurable results.
              </p>
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="grid grid-cols-3 gap-4 mb-8"
            >
              {stats.map((stat, index) => (
                <div key={stat.label} className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <stat.icon className="w-5 h-5 text-light-primary dark:text-dark-primary mr-2" />
                    <span className="text-2xl font-bold gradient-text">{stat.value}</span>
                  </div>
                  <p className="text-sm text-light-secondary dark:text-dark-secondary">{stat.label}</p>
                </div>
              ))}
            </motion.div>

            {/* CTAs */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
            >
              <button
                onClick={scrollToPortfolio}
                className="group inline-flex items-center px-8 py-4 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-semibold rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105"
              >
                View Case Studies
                <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </button>
              
              <button
                onClick={onContactClick}
                className="group inline-flex items-center px-8 py-4 border-2 border-light-primary dark:border-dark-primary text-light-primary dark:text-dark-primary font-semibold rounded-lg hover:bg-light-primary dark:hover:bg-dark-primary hover:text-white transition-all duration-300"
              >
                <Play className="mr-2 w-5 h-5" />
                Book a Call
              </button>
            </motion.div>
          </div>

          {/* Right Content - Image & Platform Icons */}
          <div className="relative">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="relative"
            >
              {/* Main Image */}
              <div className="relative w-80 h-80 mx-auto lg:w-96 lg:h-96">
                <div className="absolute inset-0 bg-gradient-primary rounded-full opacity-20 animate-pulse" />
                <div className="relative w-full h-full rounded-full overflow-hidden border-4 border-light-primary dark:border-dark-primary shadow-2xl">
                  <Image
                    src="/Denis-Personal-picture.png"
                    alt="Denis - AI & Automation Specialist"
                    fill
                    className="object-cover"
                    priority
                  />
                </div>
              </div>

              {/* Floating Platform Icons */}
              {platformIcons.map((platform, index) => (
                <motion.div
                  key={platform.name}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ 
                    duration: 0.5, 
                    delay: 0.6 + index * 0.1,
                    type: "spring",
                    stiffness: 200 
                  }}
                  className="absolute w-16 h-16 bg-white dark:bg-dark-card rounded-full shadow-lg flex items-center justify-center text-2xl hover:scale-110 transition-transform duration-300 cursor-pointer"
                  style={{
                    top: `${15 + Math.sin(index * 1.2) * 30}%`,
                    left: `${10 + Math.cos(index * 1.2) * 35}%`,
                    transform: index % 2 === 0 ? 'translateX(100%)' : 'translateX(-50%)',
                  }}
                  animate={{
                    y: [0, -10, 0],
                  }}
                  transition={{
                    duration: 3 + index * 0.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                  title={platform.name}
                >
                  {platform.icon}
                </motion.div>
              ))}
            </motion.div>

            {/* Experience Badge */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="absolute -bottom-6 -right-6 bg-white dark:bg-dark-card rounded-2xl p-4 shadow-xl border border-light-primary/20 dark:border-dark-primary/20"
            >
              <div className="text-center">
                <div className="text-2xl font-bold gradient-text">5+</div>
                <div className="text-sm text-light-secondary dark:text-dark-secondary">Years Experience</div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-6 h-10 border-2 border-light-primary dark:border-dark-primary rounded-full flex justify-center"
          >
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-1 h-3 bg-light-primary dark:bg-dark-primary rounded-full mt-2"
            />
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
