'use client';

import Image from 'next/image';
import { ArrowRight, Play } from 'lucide-react';

interface HeroProps {
  onContactClick: () => void;
}

export function Hero({ onContactClick }: HeroProps) {
  const scrollToPortfolio = () => {
    const element = document.querySelector('#portfolio');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="home" className="pt-16 pb-20 bg-gradient-to-br from-blue-50 via-white to-cyan-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center min-h-[80vh]">
          {/* Left Content */}
          <div className="text-center lg:text-left">
            <div className="mb-8">
              <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6">
                <span className="w-2 h-2 bg-blue-600 rounded-full mr-2"></span>
                AI & Automation Specialist
              </div>

              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                Transform Your Business with
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600"> AI-Powered </span>
                Solutions
              </h1>

              <p className="text-xl text-gray-600 max-w-2xl mx-auto lg:mx-0 leading-relaxed mb-8">
                From Kenya to global success, I help businesses unlock their potential through
                cutting-edge AI solutions and automation strategies that deliver measurable results.
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 mb-10">
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 mb-1">150+</div>
                <p className="text-sm text-gray-600">Projects Completed</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 mb-1">50+</div>
                <p className="text-sm text-gray-600">Happy Clients</p>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-gray-900 mb-1">98%</div>
                <p className="text-sm text-gray-600">Success Rate</p>
              </div>
            </div>

            {/* CTAs */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <button
                onClick={scrollToPortfolio}
                className="inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200"
              >
                View Case Studies
                <ArrowRight className="ml-2 w-5 h-5" />
              </button>

              <button
                onClick={onContactClick}
                className="inline-flex items-center px-8 py-4 border-2 border-blue-600 text-blue-600 font-semibold rounded-lg hover:bg-blue-600 hover:text-white transition-all duration-200"
              >
                <Play className="mr-2 w-5 h-5" />
                Book a Call
              </button>
            </div>
          </div>

          {/* Right Content - Professional Image */}
          <div className="relative">
            <div className="relative w-full max-w-lg mx-auto">
              {/* Main Image */}
              <div className="relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-blue-100 to-cyan-100">
                <Image
                  src="/Denis-Personal-picture.png"
                  alt="Denis - AI & Automation Specialist"
                  fill
                  className="object-cover"
                  priority
                />
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                AI
              </div>

              <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-cyan-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg">
                5+
              </div>

              <div className="absolute top-1/2 -left-6 w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg">
                ML
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="text-center mt-16">
          <div className="inline-flex items-center text-gray-500 text-sm">
            <div className="w-6 h-10 border-2 border-gray-300 rounded-full flex justify-center mr-3">
              <div className="w-1 h-3 bg-gray-400 rounded-full mt-2 animate-bounce"></div>
            </div>
            Scroll to explore
          </div>
        </div>
      </div>
    </section>
  );
}
