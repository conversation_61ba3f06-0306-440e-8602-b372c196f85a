import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Dark Theme Colors
        dark: {
          primary: '#17b8dd',
          'primary-deep': '#2da8c7',
          success: '#51b85f',
          warning: '#f1ad4e',
          background: '#1a1a23',
          card: 'rgba(37, 42, 46, 0.8)',
          'card-transparent': 'rgba(66, 184, 221, 0.1)',
          'glass': 'rgba(66, 184, 221, 0.05)',
          'h1': '#23b6dd',
          'h1-end': '#23a6c7',
          'h2': '#e4e4eb',
          'h3': '#23b6dd',
          'body': '#c4c5c9',
          'secondary': '#8b8b9b',
          'neon': '#42b6dd',
          'neon-subtle': 'rgba(66, 184, 221, 0.1)',
          'enabled': '#4fb55f',
          'disabled': '#8f8f8f',
        },
        // Light Theme Colors
        light: {
          primary: '#0891b2',
          'primary-deep': '#0e7490',
          success: '#16a34a',
          warning: '#ea580c',
          background: '#ffffff',
          card: 'rgba(248, 250, 252, 0.8)',
          'card-transparent': 'rgba(8, 145, 178, 0.05)',
          'glass': 'rgba(8, 145, 178, 0.03)',
          'h1': '#0891b2',
          'h1-end': '#0e7490',
          'h2': '#1f2937',
          'h3': '#0891b2',
          'body': '#374151',
          'secondary': '#6b7280',
          'neon': '#0891b2',
          'neon-subtle': 'rgba(8, 145, 178, 0.1)',
          'enabled': '#16a34a',
          'disabled': '#9ca3af',
        },
        // Shared colors that work in both themes
        primary: {
          DEFAULT: 'var(--color-primary)',
          deep: 'var(--color-primary-deep)',
        },
        success: 'var(--color-success)',
        warning: 'var(--color-warning)',
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, var(--color-h1), var(--color-h1-end))',
        'gradient-primary-button': 'linear-gradient(135deg, #23b6dd, #23a6c7)',
        'gradient-primary-button-light': 'linear-gradient(135deg, #0891b2, #0e7490)',
      },
      animation: {
        'slide-left': 'slideLeft 20s linear infinite',
        'slide-right': 'slideRight 25s linear infinite',
        'fade-in': 'fadeIn 0.6s ease-out',
        'fade-in-up': 'fadeInUp 0.8s ease-out',
        'float': 'float 3s ease-in-out infinite',
      },
      keyframes: {
        slideLeft: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(-50%)' },
        },
        slideRight: {
          '0%': { transform: 'translateX(-50%)' },
          '100%': { transform: 'translateX(0)' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        fadeInUp: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
      },
      boxShadow: {
        'neon': '0 0 20px rgba(66, 184, 221, 0.3)',
        'neon-subtle': '0 0 10px rgba(66, 184, 221, 0.1)',
        'card': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'card-hover': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      },
    },
  },
  plugins: [],
};
export default config;
