{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/Hero.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\nimport { ArrowRight, Play } from 'lucide-react';\n\ninterface HeroProps {\n  onContactClick: () => void;\n}\n\nexport function Hero({ onContactClick }: HeroProps) {\n  const scrollToPortfolio = () => {\n    const element = document.querySelector('#portfolio');\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <section id=\"home\" className=\"pt-16 pb-20 bg-gradient-to-br from-blue-50 via-white to-cyan-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid lg:grid-cols-2 gap-12 lg:gap-16 items-center min-h-[80vh]\">\n          {/* Left Content */}\n          <div className=\"text-center lg:text-left\">\n            <div className=\"mb-8\">\n              <div className=\"inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6\">\n                <span className=\"w-2 h-2 bg-blue-600 rounded-full mr-2\"></span>\n                AI & Automation Specialist\n              </div>\n\n              <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6\">\n                Transform Your Business with\n                <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600\"> AI-Powered </span>\n                Solutions\n              </h1>\n\n              <p className=\"text-xl text-gray-600 max-w-2xl mx-auto lg:mx-0 leading-relaxed mb-8\">\n                From Kenya to global success, I help businesses unlock their potential through\n                cutting-edge AI solutions and automation strategies that deliver measurable results.\n              </p>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-3 gap-8 mb-10\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-gray-900 mb-1\">150+</div>\n                <p className=\"text-sm text-gray-600\">Projects Completed</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-gray-900 mb-1\">50+</div>\n                <p className=\"text-sm text-gray-600\">Happy Clients</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-gray-900 mb-1\">98%</div>\n                <p className=\"text-sm text-gray-600\">Success Rate</p>\n              </div>\n            </div>\n\n            {/* CTAs */}\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\">\n              <button\n                onClick={scrollToPortfolio}\n                className=\"inline-flex items-center px-8 py-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors duration-200\"\n              >\n                View Case Studies\n                <ArrowRight className=\"ml-2 w-5 h-5\" />\n              </button>\n\n              <button\n                onClick={onContactClick}\n                className=\"inline-flex items-center px-8 py-4 border-2 border-blue-600 text-blue-600 font-semibold rounded-lg hover:bg-blue-600 hover:text-white transition-all duration-200\"\n              >\n                <Play className=\"mr-2 w-5 h-5\" />\n                Book a Call\n              </button>\n            </div>\n          </div>\n\n          {/* Right Content - Professional Image */}\n          <div className=\"relative\">\n            <div className=\"relative w-full max-w-lg mx-auto\">\n              {/* Main Image */}\n              <div className=\"relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-blue-100 to-cyan-100\">\n                <Image\n                  src=\"/Denis-Personal-picture.png\"\n                  alt=\"Denis - AI & Automation Specialist\"\n                  fill\n                  className=\"object-cover\"\n                  priority\n                />\n              </div>\n\n              {/* Floating Elements */}\n              <div className=\"absolute -top-4 -right-4 w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg\">\n                AI\n              </div>\n\n              <div className=\"absolute -bottom-4 -left-4 w-16 h-16 bg-cyan-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg\">\n                5+\n              </div>\n\n              <div className=\"absolute top-1/2 -left-6 w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg\">\n                ML\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <div className=\"text-center mt-16\">\n          <div className=\"inline-flex items-center text-gray-500 text-sm\">\n            <div className=\"w-6 h-10 border-2 border-gray-300 rounded-full flex justify-center mr-3\">\n              <div className=\"w-1 h-3 bg-gray-400 rounded-full mt-2 animate-bounce\"></div>\n            </div>\n            Scroll to explore\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AASO,SAAS,KAAK,EAAE,cAAc,EAAa;IAChD,MAAM,oBAAoB;QACxB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAO,WAAU;kBAC3B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;;;;;;gDAA+C;;;;;;;sDAIjE,8OAAC;4CAAG,WAAU;;gDAA8E;8DAE1F,8OAAC;oDAAK,WAAU;8DAA2E;;;;;;gDAAmB;;;;;;;sDAIhH,8OAAC;4CAAE,WAAU;sDAAuE;;;;;;;;;;;;8CAOtF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAEvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAKzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;;gDACX;8DAEC,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAGxB,8OAAC;4CACC,SAAS;4CACT,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;sCAOvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;4CACV,QAAQ;;;;;;;;;;;kDAKZ,8OAAC;wCAAI,WAAU;kDAAsI;;;;;;kDAIrJ,8OAAC;wCAAI,WAAU;kDAAgI;;;;;;kDAI/I,8OAAC;wCAAI,WAAU;kDAAwI;;;;;;;;;;;;;;;;;;;;;;;8BAQ7J,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;;4BACX;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/BlogSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { Clock, ArrowRight, Tag } from 'lucide-react';\nimport type { BlogPost } from '@/types';\n\nconst blogPosts: BlogPost[] = [\n  {\n    id: '1',\n    title: 'The Future of AI in Business Automation',\n    excerpt: 'Discover how artificial intelligence is revolutionizing business processes and creating unprecedented opportunities for growth and efficiency.',\n    readTime: '5 min read',\n    category: 'AI Strategy',\n    image: '/placeholder.png',\n    slug: 'future-ai-business-automation',\n    publishedAt: '2024-01-15',\n  },\n  {\n    id: '2',\n    title: 'Building Scalable Automation Workflows',\n    excerpt: 'Learn the essential principles and best practices for creating automation systems that grow with your business needs.',\n    readTime: '7 min read',\n    category: 'Automation',\n    image: '/placeholder.png',\n    slug: 'scalable-automation-workflows',\n    publishedAt: '2024-01-10',\n  },\n  {\n    id: '3',\n    title: 'Data-Driven Decision Making with AI',\n    excerpt: 'Transform your business intelligence with AI-powered analytics that provide actionable insights and predictive capabilities.',\n    readTime: '6 min read',\n    category: 'Data Science',\n    image: '/placeholder.png',\n    slug: 'data-driven-ai-decisions',\n    publishedAt: '2024-01-05',\n  },\n];\n\nconst categoryColors = {\n  'AI Strategy': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',\n  'Automation': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',\n  'Data Science': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',\n};\n\nexport function BlogSection() {\n  return (\n    <section id=\"insights\" className=\"py-20 bg-light-background dark:bg-dark-background\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <span className=\"inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4\">\n            📚 Latest Insights\n          </span>\n          \n          <h2 className=\"text-4xl sm:text-5xl font-bold gradient-text mb-6\">\n            Latest AI and Automation Insights\n          </h2>\n          \n          <p className=\"text-lg text-light-secondary dark:text-dark-secondary max-w-3xl mx-auto\">\n            Stay ahead of the curve with cutting-edge insights, practical strategies, and real-world \n            applications of AI and automation technologies.\n          </p>\n        </motion.div>\n\n        {/* Blog Cards Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {blogPosts.map((post, index) => (\n            <motion.article\n              key={post.id}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"group bg-white dark:bg-dark-card rounded-2xl overflow-hidden shadow-card hover:shadow-card-hover transition-all duration-300 hover:-translate-y-2\"\n            >\n              {/* Image */}\n              <div className=\"relative h-48 overflow-hidden\">\n                <Image\n                  src={post.image}\n                  alt={post.title}\n                  fill\n                  className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\" />\n                \n                {/* Category Tag */}\n                <div className=\"absolute top-4 left-4\">\n                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${\n                    categoryColors[post.category as keyof typeof categoryColors]\n                  }`}>\n                    <Tag className=\"w-3 h-3 mr-1\" />\n                    {post.category}\n                  </span>\n                </div>\n              </div>\n\n              {/* Content */}\n              <div className=\"p-6\">\n                <div className=\"flex items-center text-sm text-light-secondary dark:text-dark-secondary mb-3\">\n                  <Clock className=\"w-4 h-4 mr-1\" />\n                  {post.readTime}\n                  <span className=\"mx-2\">•</span>\n                  <time dateTime={post.publishedAt}>\n                    {new Date(post.publishedAt).toLocaleDateString('en-US', {\n                      month: 'short',\n                      day: 'numeric',\n                      year: 'numeric',\n                    })}\n                  </time>\n                </div>\n\n                <h3 className=\"text-xl font-bold text-light-h2 dark:text-dark-h2 mb-3 group-hover:text-light-primary dark:group-hover:text-dark-primary transition-colors\">\n                  {post.title}\n                </h3>\n\n                <p className=\"text-light-secondary dark:text-dark-secondary mb-4 line-clamp-3\">\n                  {post.excerpt}\n                </p>\n\n                {/* Read More Link */}\n                <button className=\"group/btn inline-flex items-center text-light-primary dark:text-dark-primary font-medium hover:text-light-primary-deep dark:hover:text-dark-primary-deep transition-colors\">\n                  Read More\n                  <ArrowRight className=\"ml-2 w-4 h-4 group-hover/btn:translate-x-1 transition-transform\" />\n                </button>\n              </div>\n            </motion.article>\n          ))}\n        </div>\n\n        {/* View All Button */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-12\"\n        >\n          <button className=\"inline-flex items-center px-8 py-4 bg-light-card dark:bg-dark-card border-2 border-light-primary dark:border-dark-primary text-light-primary dark:text-dark-primary font-semibold rounded-lg hover:bg-light-primary dark:hover:bg-dark-primary hover:text-white transition-all duration-300 hover:scale-105\">\n            View All Articles\n            <ArrowRight className=\"ml-2 w-5 h-5\" />\n          </button>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAOA,MAAM,YAAwB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,OAAO;QACP,MAAM;QACN,aAAa;IACf;CACD;AAED,MAAM,iBAAiB;IACrB,eAAe;IACf,cAAc;IACd,gBAAgB;AAClB;AAEO,SAAS;IACd,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAK,WAAU;sCAAqJ;;;;;;sCAIrK,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAIlE,8OAAC;4BAAE,WAAU;sCAA0E;;;;;;;;;;;;8BAOzF,8OAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,OAAO;4BAEb,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,KAAK,KAAK;4CACf,KAAK,KAAK,KAAK;4CACf,IAAI;4CACJ,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;;;;;sDAGf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAW,CAAC,oEAAoE,EACpF,cAAc,CAAC,KAAK,QAAQ,CAAgC,EAC5D;;kEACA,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDACd,KAAK,QAAQ;;;;;;;;;;;;;;;;;;8CAMpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAChB,KAAK,QAAQ;8DACd,8OAAC;oDAAK,WAAU;8DAAO;;;;;;8DACvB,8OAAC;oDAAK,UAAU,KAAK,WAAW;8DAC7B,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC,SAAS;wDACtD,OAAO;wDACP,KAAK;wDACL,MAAM;oDACR;;;;;;;;;;;;sDAIJ,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAGb,8OAAC;4CAAE,WAAU;sDACV,KAAK,OAAO;;;;;;sDAIf,8OAAC;4CAAO,WAAU;;gDAA6K;8DAE7L,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;2BAtDrB,KAAK,EAAE;;;;;;;;;;8BA8DlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAO,WAAU;;4BAA8S;0CAE9T,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC", "debugId": null}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/PersonalStory.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { MapPin, TrendingUp, Users, Award, ArrowRight } from 'lucide-react';\n\nconst highlights = [\n  {\n    icon: MapPin,\n    title: 'From Kenya to Global',\n    description: 'Started in Nairobi, now serving clients worldwide',\n  },\n  {\n    icon: TrendingUp,\n    title: '150+ Projects Delivered',\n    description: 'Consistent track record of successful implementations',\n  },\n  {\n    icon: Users,\n    title: '50+ Happy Clients',\n    description: 'Building lasting relationships through exceptional results',\n  },\n  {\n    icon: Award,\n    title: '98% Success Rate',\n    description: 'Proven methodology that delivers measurable outcomes',\n  },\n];\n\nconst journeySteps = [\n  {\n    year: '2019',\n    title: 'The Beginning',\n    description: 'Started as a curious developer in Nairobi, fascinated by the potential of automation',\n  },\n  {\n    year: '2021',\n    title: 'First Breakthrough',\n    description: 'Developed my first AI-powered business solution, saving a client 40 hours per week',\n  },\n  {\n    year: '2022',\n    title: 'Global Expansion',\n    description: '<PERSON>gan working with international clients, scaling solutions across different markets',\n  },\n  {\n    year: '2024',\n    title: 'AI Specialist',\n    description: 'Now recognized as a leading expert in AI and automation solutions',\n  },\n];\n\nexport function PersonalStory() {\n  return (\n    <section id=\"story\" className=\"py-20 bg-light-card dark:bg-dark-card\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n          {/* Left Side - Image/Visual */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"relative\"\n          >\n            {/* Main Image */}\n            <div className=\"relative\">\n              <div className=\"relative w-full h-96 rounded-2xl overflow-hidden shadow-2xl\">\n                <Image\n                  src=\"/placeholder.png\"\n                  alt=\"Denis working on AI solutions\"\n                  fill\n                  className=\"object-cover\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent\" />\n              </div>\n              \n              {/* Floating Stats */}\n              <motion.div\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.8, delay: 0.3 }}\n                viewport={{ once: true }}\n                className=\"absolute -top-6 -right-6 bg-white dark:bg-dark-background rounded-2xl p-6 shadow-xl border border-light-primary/20 dark:border-dark-primary/20\"\n              >\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold gradient-text\">5+</div>\n                  <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Years</div>\n                  <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Experience</div>\n                </div>\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.8, delay: 0.5 }}\n                viewport={{ once: true }}\n                className=\"absolute -bottom-6 -left-6 bg-white dark:bg-dark-background rounded-2xl p-6 shadow-xl border border-light-primary/20 dark:border-dark-primary/20\"\n              >\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold gradient-text\">98%</div>\n                  <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Success</div>\n                  <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Rate</div>\n                </div>\n              </motion.div>\n            </div>\n          </motion.div>\n\n          {/* Right Side - Story Content */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            {/* Header */}\n            <div>\n              <span className=\"inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4\">\n                🚀 My Journey\n              </span>\n              \n              <h2 className=\"text-4xl sm:text-5xl font-bold mb-6\">\n                <span className=\"text-light-h2 dark:text-dark-h2\">The Journey to</span>\n                <br />\n                <span className=\"gradient-text\">AI Specialist</span>\n              </h2>\n              \n              <h3 className=\"text-2xl font-semibold text-light-h3 dark:text-dark-h3 mb-6\">\n                Turning Challenges Into Opportunities\n              </h3>\n            </div>\n\n            {/* Story Content */}\n            <div className=\"space-y-6\">\n              <p className=\"text-lg text-light-body dark:text-dark-body leading-relaxed\">\n                My journey began in Nairobi, Kenya, where I discovered the transformative power of \n                technology. What started as curiosity about automation quickly evolved into a passion \n                for solving complex business challenges through AI and intelligent systems.\n              </p>\n              \n              <p className=\"text-lg text-light-body dark:text-dark-body leading-relaxed\">\n                From those early days of experimenting with simple scripts to now architecting \n                enterprise-level AI solutions, every project has been a stepping stone toward \n                mastering the art of digital transformation.\n              </p>\n              \n              <p className=\"text-lg text-light-body dark:text-dark-body leading-relaxed\">\n                Today, I help businesses worldwide unlock their potential through cutting-edge \n                automation strategies, proving that with the right approach, any challenge can \n                become an opportunity for growth.\n              </p>\n            </div>\n\n            {/* Highlights Grid */}\n            <div className=\"grid grid-cols-2 gap-4 mt-8\">\n              {highlights.map((highlight, index) => (\n                <motion.div\n                  key={highlight.title}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"p-4 bg-white dark:bg-dark-background rounded-xl border border-light-primary/10 dark:border-dark-primary/10\"\n                >\n                  <highlight.icon className=\"w-6 h-6 text-light-primary dark:text-dark-primary mb-2\" />\n                  <h4 className=\"font-semibold text-light-h3 dark:text-dark-h3 text-sm mb-1\">\n                    {highlight.title}\n                  </h4>\n                  <p className=\"text-xs text-light-secondary dark:text-dark-secondary\">\n                    {highlight.description}\n                  </p>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* CTA */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              viewport={{ once: true }}\n              className=\"pt-6\"\n            >\n              <button className=\"group inline-flex items-center px-8 py-4 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-semibold rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105\">\n                Let's Build Something Amazing\n                <ArrowRight className=\"ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n              </button>\n            </motion.div>\n          </motion.div>\n        </div>\n\n        {/* Journey Timeline */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"mt-20\"\n        >\n          <h3 className=\"text-3xl font-bold text-center gradient-text mb-12\">\n            The Journey Timeline\n          </h3>\n          \n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {journeySteps.map((step, index) => (\n              <motion.div\n                key={step.year}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"text-center\"\n              >\n                <div className=\"relative\">\n                  <div className=\"w-16 h-16 mx-auto bg-gradient-primary-button dark:bg-gradient-primary-button rounded-full flex items-center justify-center text-white font-bold text-lg mb-4\">\n                    {step.year}\n                  </div>\n                  {index < journeySteps.length - 1 && (\n                    <div className=\"hidden lg:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-light-primary to-light-primary/20 dark:from-dark-primary dark:to-dark-primary/20\" />\n                  )}\n                </div>\n                <h4 className=\"font-bold text-light-h3 dark:text-dark-h3 mb-2\">\n                  {step.title}\n                </h4>\n                <p className=\"text-sm text-light-secondary dark:text-dark-secondary\">\n                  {step.description}\n                </p>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,aAAa;IACjB;QACE,MAAM,0MAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,kNAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,eAAe;IACnB;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;IACf;CACD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAGV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,8OAAC;oDAAI,WAAU;8DAAwD;;;;;;8DACvE,8OAAC;oDAAI,WAAU;8DAAwD;;;;;;;;;;;;;;;;;kDAI3E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAmC;;;;;;8DAClD,8OAAC;oDAAI,WAAU;8DAAwD;;;;;;8DACvE,8OAAC;oDAAI,WAAU;8DAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO/E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAGV,8OAAC;;sDACC,8OAAC;4CAAK,WAAU;sDAAqJ;;;;;;sDAIrK,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAkC;;;;;;8DAClD,8OAAC;;;;;8DACD,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAGlC,8OAAC;4CAAG,WAAU;sDAA8D;;;;;;;;;;;;8CAM9E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAA8D;;;;;;sDAM3E,8OAAC;4CAAE,WAAU;sDAA8D;;;;;;sDAM3E,8OAAC;4CAAE,WAAU;sDAA8D;;;;;;;;;;;;8CAQ7E,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,aAAa;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAI;4CACtD,UAAU;gDAAE,MAAM;4CAAK;4CACvB,WAAU;;8DAEV,8OAAC,UAAU,IAAI;oDAAC,WAAU;;;;;;8DAC1B,8OAAC;oDAAG,WAAU;8DACX,UAAU,KAAK;;;;;;8DAElB,8OAAC;oDAAE,WAAU;8DACV,UAAU,WAAW;;;;;;;2CAZnB,UAAU,KAAK;;;;;;;;;;8CAmB1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;8CAEV,cAAA,8OAAC;wCAAO,WAAU;;4CAAwM;0DAExN,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAInE,8OAAC;4BAAI,WAAU;sCACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,KAAK,IAAI;;;;;;gDAEX,QAAQ,aAAa,MAAM,GAAG,mBAC7B,8OAAC;oDAAI,WAAU;;;;;;;;;;;;sDAGnB,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;;mCAnBd,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4B9B", "debugId": null}}, {"offset": {"line": 1237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/CaseStudyShowcase.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Image from 'next/image';\nimport { ArrowRight, TrendingUp, Clock, Target, BarChart3 } from 'lucide-react';\nimport type { CaseStudy } from '@/types';\n\nconst featuredCaseStudy: CaseStudy = {\n  id: '1',\n  title: 'Content & Marketing Analytics Transformation',\n  description: 'Revolutionized a digital marketing agency\\'s analytics workflow with AI-powered automation, delivering unprecedented insights and efficiency gains.',\n  metrics: [\n    {\n      label: 'Audience Reach Growth',\n      value: '120%',\n      improvement: '+85% vs previous quarter'\n    },\n    {\n      label: 'Project Timeline',\n      value: '2 Months',\n      improvement: '50% faster than estimated'\n    },\n    {\n      label: 'Monthly Engagement',\n      value: '60%+',\n      improvement: 'Sustained growth rate'\n    },\n    {\n      label: 'Cost Reduction',\n      value: '40%',\n      improvement: 'In manual analysis time'\n    }\n  ],\n  timeline: '2 months',\n  image: '/placeholder.png',\n  slug: 'content-marketing-analytics-transformation'\n};\n\nconst additionalCaseStudies = [\n  {\n    id: '2',\n    title: 'E-commerce Automation Suite',\n    description: 'Streamlined inventory management and customer service for a growing e-commerce platform.',\n    metrics: '300% efficiency increase',\n    image: '/placeholder.png'\n  },\n  {\n    id: '3',\n    title: 'Healthcare Data Processing',\n    description: 'Automated patient data analysis and reporting for a healthcare network.',\n    metrics: '80% time savings',\n    image: '/placeholder.png'\n  },\n  {\n    id: '4',\n    title: 'Financial Risk Assessment',\n    description: 'AI-powered risk analysis system for a fintech startup.',\n    metrics: '95% accuracy rate',\n    image: '/placeholder.png'\n  }\n];\n\nconst technologies = [\n  { name: 'Python', icon: '🐍' },\n  { name: 'TensorFlow', icon: '🧠' },\n  { name: 'Google Analytics', icon: '📊' },\n  { name: 'Power BI', icon: '📈' },\n  { name: 'AWS', icon: '☁️' },\n  { name: 'Docker', icon: '🐳' }\n];\n\nexport function CaseStudyShowcase() {\n  return (\n    <section id=\"case-studies\" className=\"py-20 bg-light-card dark:bg-dark-card\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <span className=\"inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4\">\n            🎯 Success Stories\n          </span>\n          \n          <h2 className=\"text-4xl sm:text-5xl font-bold gradient-text mb-6\">\n            Transformative Results\n          </h2>\n          \n          <p className=\"text-lg text-light-secondary dark:text-dark-secondary max-w-3xl mx-auto\">\n            Real projects, real impact. See how AI and automation have transformed businesses \n            across different industries.\n          </p>\n        </motion.div>\n\n        {/* Featured Case Study */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"bg-white dark:bg-dark-background rounded-3xl overflow-hidden shadow-2xl mb-16\"\n        >\n          <div className=\"grid lg:grid-cols-2 gap-0\">\n            {/* Left Side - Content */}\n            <div className=\"p-8 lg:p-12\">\n              <div className=\"mb-6\">\n                <div className=\"flex items-center mb-4\">\n                  <Target className=\"w-6 h-6 text-light-primary dark:text-dark-primary mr-2\" />\n                  <span className=\"text-sm font-medium text-light-primary dark:text-dark-primary\">\n                    Featured Case Study\n                  </span>\n                </div>\n                \n                <h3 className=\"text-3xl font-bold text-light-h2 dark:text-dark-h2 mb-4\">\n                  {featuredCaseStudy.title}\n                </h3>\n                \n                <p className=\"text-lg text-light-body dark:text-dark-body leading-relaxed mb-6\">\n                  {featuredCaseStudy.description}\n                </p>\n              </div>\n\n              {/* Metrics Grid */}\n              <div className=\"grid grid-cols-2 gap-4 mb-8\">\n                {featuredCaseStudy.metrics.map((metric, index) => (\n                  <motion.div\n                    key={metric.label}\n                    initial={{ opacity: 0, scale: 0.8 }}\n                    whileInView={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.6, delay: index * 0.1 }}\n                    viewport={{ once: true }}\n                    className=\"p-4 bg-light-background dark:bg-dark-card rounded-xl border border-light-primary/10 dark:border-dark-primary/10\"\n                  >\n                    <div className=\"text-2xl font-bold gradient-text mb-1\">\n                      {metric.value}\n                    </div>\n                    <div className=\"text-sm font-medium text-light-h3 dark:text-dark-h3 mb-1\">\n                      {metric.label}\n                    </div>\n                    <div className=\"text-xs text-light-secondary dark:text-dark-secondary\">\n                      {metric.improvement}\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n\n              {/* Technologies Used */}\n              <div className=\"mb-8\">\n                <h4 className=\"text-sm font-medium text-light-secondary dark:text-dark-secondary mb-3\">\n                  Technologies Used:\n                </h4>\n                <div className=\"flex flex-wrap gap-2\">\n                  {technologies.map((tech) => (\n                    <span\n                      key={tech.name}\n                      className=\"inline-flex items-center px-3 py-1 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium\"\n                    >\n                      <span className=\"mr-1\">{tech.icon}</span>\n                      {tech.name}\n                    </span>\n                  ))}\n                </div>\n              </div>\n\n              {/* CTA */}\n              <button className=\"group inline-flex items-center px-8 py-4 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-semibold rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105\">\n                Read Full Case Study\n                <ArrowRight className=\"ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n              </button>\n            </div>\n\n            {/* Right Side - Analytics Dashboard Image */}\n            <div className=\"relative lg:h-auto h-64\">\n              <Image\n                src={featuredCaseStudy.image}\n                alt=\"Analytics Dashboard\"\n                fill\n                className=\"object-cover\"\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent\" />\n              \n              {/* Floating Metrics */}\n              <motion.div\n                initial={{ opacity: 0, x: 50 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.8, delay: 0.3 }}\n                viewport={{ once: true }}\n                className=\"absolute top-6 right-6 bg-white dark:bg-dark-background rounded-xl p-4 shadow-lg\"\n              >\n                <div className=\"flex items-center mb-2\">\n                  <TrendingUp className=\"w-5 h-5 text-green-500 mr-2\" />\n                  <span className=\"text-sm font-medium text-light-h3 dark:text-dark-h3\">\n                    Live Metrics\n                  </span>\n                </div>\n                <div className=\"text-2xl font-bold gradient-text\">120%</div>\n                <div className=\"text-xs text-light-secondary dark:text-dark-secondary\">\n                  Growth Rate\n                </div>\n              </motion.div>\n\n              <motion.div\n                initial={{ opacity: 0, x: 50 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.8, delay: 0.5 }}\n                viewport={{ once: true }}\n                className=\"absolute bottom-6 right-6 bg-white dark:bg-dark-background rounded-xl p-4 shadow-lg\"\n              >\n                <div className=\"flex items-center mb-2\">\n                  <Clock className=\"w-5 h-5 text-blue-500 mr-2\" />\n                  <span className=\"text-sm font-medium text-light-h3 dark:text-dark-h3\">\n                    Timeline\n                  </span>\n                </div>\n                <div className=\"text-2xl font-bold gradient-text\">2 Mo</div>\n                <div className=\"text-xs text-light-secondary dark:text-dark-secondary\">\n                  Completion\n                </div>\n              </motion.div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Additional Case Studies */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          viewport={{ once: true }}\n        >\n          <h3 className=\"text-2xl font-bold text-center gradient-text mb-8\">\n            More Success Stories\n          </h3>\n          \n          <div className=\"grid md:grid-cols-3 gap-6\">\n            {additionalCaseStudies.map((study, index) => (\n              <motion.div\n                key={study.id}\n                initial={{ opacity: 0, y: 30 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"group bg-white dark:bg-dark-background rounded-2xl overflow-hidden shadow-card hover:shadow-card-hover transition-all duration-300 hover:-translate-y-2\"\n              >\n                <div className=\"relative h-48\">\n                  <Image\n                    src={study.image}\n                    alt={study.title}\n                    fill\n                    className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                  />\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\" />\n                  <div className=\"absolute bottom-4 left-4 right-4\">\n                    <div className=\"text-white font-bold text-lg mb-1\">\n                      {study.metrics}\n                    </div>\n                  </div>\n                </div>\n                \n                <div className=\"p-6\">\n                  <h4 className=\"font-bold text-light-h3 dark:text-dark-h3 mb-2 group-hover:text-light-primary dark:group-hover:text-dark-primary transition-colors\">\n                    {study.title}\n                  </h4>\n                  <p className=\"text-sm text-light-secondary dark:text-dark-secondary line-clamp-2\">\n                    {study.description}\n                  </p>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* CTA Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16 pt-16 border-t border-light-primary/20 dark:border-dark-primary/20\"\n        >\n          <h3 className=\"text-2xl font-bold text-light-h2 dark:text-dark-h2 mb-4\">\n            Ready to Transform Your Business?\n          </h3>\n          <p className=\"text-light-secondary dark:text-dark-secondary mb-8 max-w-2xl mx-auto\">\n            Let's discuss how AI and automation can drive similar results for your organization.\n          </p>\n          <button className=\"group inline-flex items-center px-8 py-4 bg-light-card dark:bg-dark-card border-2 border-light-primary dark:border-dark-primary text-light-primary dark:text-dark-primary font-semibold rounded-lg hover:bg-light-primary dark:hover:bg-dark-primary hover:text-white transition-all duration-300 hover:scale-105\">\n            View All Case Studies\n            <BarChart3 className=\"ml-2 w-5 h-5\" />\n          </button>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAOA,MAAM,oBAA+B;IACnC,IAAI;IACJ,OAAO;IACP,aAAa;IACb,SAAS;QACP;YACE,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;QACf;KACD;IACD,UAAU;IACV,OAAO;IACP,MAAM;AACR;AAEA,MAAM,wBAAwB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,SAAS;QACT,OAAO;IACT;CACD;AAED,MAAM,eAAe;IACnB;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAO,MAAM;IAAK;IAC1B;QAAE,MAAM;QAAU,MAAM;IAAK;CAC9B;AAEM,SAAS;IACd,qBACE,8OAAC;QAAQ,IAAG;QAAe,WAAU;kBACnC,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAK,WAAU;sCAAqJ;;;;;;sCAIrK,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAIlE,8OAAC;4BAAE,WAAU;sCAA0E;;;;;;;;;;;;8BAOzF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAK,WAAU;kEAAgE;;;;;;;;;;;;0DAKlF,8OAAC;gDAAG,WAAU;0DACX,kBAAkB,KAAK;;;;;;0DAG1B,8OAAC;gDAAE,WAAU;0DACV,kBAAkB,WAAW;;;;;;;;;;;;kDAKlC,8OAAC;wCAAI,WAAU;kDACZ,kBAAkB,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,aAAa;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDACpC,YAAY;oDAAE,UAAU;oDAAK,OAAO,QAAQ;gDAAI;gDAChD,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACZ,OAAO,KAAK;;;;;;kEAEf,8OAAC;wDAAI,WAAU;kEACZ,OAAO,KAAK;;;;;;kEAEf,8OAAC;wDAAI,WAAU;kEACZ,OAAO,WAAW;;;;;;;+CAdhB,OAAO,KAAK;;;;;;;;;;kDAqBvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyE;;;;;;0DAGvF,8OAAC;gDAAI,WAAU;0DACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;wDAEC,WAAU;;0EAEV,8OAAC;gEAAK,WAAU;0EAAQ,KAAK,IAAI;;;;;;4DAChC,KAAK,IAAI;;uDAJL,KAAK,IAAI;;;;;;;;;;;;;;;;kDAWtB,8OAAC;wCAAO,WAAU;;4CAAwM;0DAExN,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;0CAK1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,kBAAkB,KAAK;wCAC5B,KAAI;wCACJ,IAAI;wCACJ,WAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;;;;;;kDAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEACtB,8OAAC;wDAAK,WAAU;kEAAsD;;;;;;;;;;;;0DAIxE,8OAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;0DAAwD;;;;;;;;;;;;kDAKzE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAsD;;;;;;;;;;;;0DAIxE,8OAAC;gDAAI,WAAU;0DAAmC;;;;;;0DAClD,8OAAC;gDAAI,WAAU;0DAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS/E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAIlE,8OAAC;4BAAI,WAAU;sCACZ,sBAAsB,GAAG,CAAC,CAAC,OAAO,sBACjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK,MAAM,KAAK;oDAChB,KAAK,MAAM,KAAK;oDAChB,IAAI;oDACJ,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACZ,MAAM,OAAO;;;;;;;;;;;;;;;;;sDAKpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,MAAM,KAAK;;;;;;8DAEd,8OAAC;oDAAE,WAAU;8DACV,MAAM,WAAW;;;;;;;;;;;;;mCA3BjB,MAAM,EAAE;;;;;;;;;;;;;;;;8BAoCrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAA0D;;;;;;sCAGxE,8OAAC;4BAAE,WAAU;sCAAuE;;;;;;sCAGpF,8OAAC;4BAAO,WAAU;;gCAAoT;8CAEpU,8OAAC,kNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjC", "debugId": null}}, {"offset": {"line": 1945, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/TestimonialWall.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Star, Play, TrendingUp, Users } from 'lucide-react';\nimport Image from 'next/image';\nimport type { TestimonialCard } from '@/types';\n\nconst testimonialCards: TestimonialCard[] = [\n  {\n    id: '1',\n    type: 'text',\n    size: 'medium',\n    content: {\n      text: \"<PERSON> transformed our entire workflow with AI automation. We're now processing 3x more data with half the manual effort!\"\n    },\n    author: {\n      name: '<PERSON>',\n      role: 'Operations Director',\n      avatar: '/placeholder.png',\n      initials: 'SJ'\n    }\n  },\n  {\n    id: '2',\n    type: 'stat',\n    size: 'small',\n    accent: 'gold',\n    content: {\n      stat: '300%',\n      metric: 'Efficiency Increase'\n    },\n    author: {\n      name: 'TechCorp',\n      role: 'Client Result',\n      initials: 'TC'\n    }\n  },\n  {\n    id: '3',\n    type: 'video',\n    size: 'large',\n    content: {\n      text: \"Watch how Denis helped us automate our customer service and reduce response time by 80%\"\n    },\n    author: {\n      name: '<PERSON>',\n      role: 'CEO, InnovateLab',\n      avatar: '/placeholder.png',\n      initials: 'MC'\n    },\n    video: {\n      thumbnail: '/placeholder.png'\n    }\n  },\n  {\n    id: '4',\n    type: 'quote',\n    size: 'medium',\n    content: {\n      quote: \"The ROI from <PERSON>'s AI solutions exceeded our expectations by 250%. Absolutely game-changing!\"\n    },\n    author: {\n      name: 'Emma Rodriguez',\n      role: 'CFO, DataFlow Inc',\n      avatar: '/placeholder.png',\n      initials: 'ER'\n    }\n  },\n  {\n    id: '5',\n    type: 'stat',\n    size: 'small',\n    accent: 'green',\n    content: {\n      stat: '40hrs',\n      metric: 'Saved Weekly'\n    },\n    author: {\n      name: 'StartupXYZ',\n      role: 'Client Result',\n      initials: 'SX'\n    }\n  },\n  {\n    id: '6',\n    type: 'text',\n    size: 'medium',\n    content: {\n      text: \"Professional, innovative, and results-driven. Denis doesn't just deliver solutions, he delivers transformation.\"\n    },\n    author: {\n      name: 'David Park',\n      role: 'CTO, FutureTech',\n      avatar: '/placeholder.png',\n      initials: 'DP'\n    }\n  },\n  {\n    id: '7',\n    type: 'stat',\n    size: 'small',\n    accent: 'gold',\n    content: {\n      stat: '98%',\n      metric: 'Client Satisfaction'\n    },\n    author: {\n      name: 'Overall Rating',\n      role: 'Client Feedback',\n      initials: 'OR'\n    }\n  },\n  {\n    id: '8',\n    type: 'text',\n    size: 'large',\n    content: {\n      text: \"From Kenya to global impact - Denis brings world-class AI expertise with a personal touch that makes all the difference.\"\n    },\n    author: {\n      name: 'Lisa Thompson',\n      role: 'VP Innovation, GlobalCorp',\n      avatar: '/placeholder.png',\n      initials: 'LT'\n    }\n  }\n];\n\n// Duplicate cards for infinite scroll effect\nconst infiniteCards = [...testimonialCards, ...testimonialCards, ...testimonialCards];\n\nfunction TestimonialCard({ card, index }: { card: TestimonialCard; index: number }) {\n  const sizeClasses = {\n    small: 'h-32',\n    medium: 'h-40',\n    large: 'h-48'\n  };\n\n  const accentClasses = {\n    gold: 'border-yellow-400 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20',\n    green: 'border-green-400 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20'\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6, delay: index * 0.1 }}\n      viewport={{ once: true }}\n      className={`\n        ${sizeClasses[card.size]} \n        ${card.accent ? accentClasses[card.accent] : 'bg-white dark:bg-dark-card border-light-primary/20 dark:border-dark-primary/20'}\n        rounded-2xl p-4 border-2 shadow-card hover:shadow-card-hover transition-all duration-300 hover:-translate-y-1 relative overflow-hidden\n      `}\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute top-0 right-0 w-20 h-20 bg-light-primary dark:bg-dark-primary rounded-full -translate-y-10 translate-x-10\" />\n      </div>\n\n      <div className=\"relative h-full flex flex-col\">\n        {/* Content based on type */}\n        {card.type === 'stat' && (\n          <div className=\"flex-1 flex flex-col justify-center text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-1\">\n              {card.content.stat}\n            </div>\n            <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">\n              {card.content.metric}\n            </div>\n          </div>\n        )}\n\n        {card.type === 'text' && (\n          <div className=\"flex-1\">\n            <p className=\"text-sm text-light-body dark:text-dark-body leading-relaxed line-clamp-4\">\n              \"{card.content.text}\"\n            </p>\n          </div>\n        )}\n\n        {card.type === 'quote' && (\n          <div className=\"flex-1\">\n            <div className=\"text-2xl text-light-primary dark:text-dark-primary mb-2\">\"</div>\n            <p className=\"text-sm text-light-body dark:text-dark-body leading-relaxed line-clamp-3\">\n              {card.content.quote}\n            </p>\n          </div>\n        )}\n\n        {card.type === 'video' && (\n          <div className=\"flex-1 relative\">\n            <div className=\"relative h-20 rounded-lg overflow-hidden mb-2\">\n              <Image\n                src={card.video?.thumbnail || '/placeholder.png'}\n                alt=\"Video thumbnail\"\n                fill\n                className=\"object-cover\"\n              />\n              <div className=\"absolute inset-0 bg-black/30 flex items-center justify-center\">\n                <Play className=\"w-6 h-6 text-white\" />\n              </div>\n            </div>\n            <p className=\"text-xs text-light-secondary dark:text-dark-secondary line-clamp-2\">\n              {card.content.text}\n            </p>\n          </div>\n        )}\n\n        {/* Author */}\n        <div className=\"flex items-center mt-3 pt-3 border-t border-light-primary/10 dark:border-dark-primary/10\">\n          <div className=\"w-8 h-8 rounded-full bg-light-primary dark:bg-dark-primary flex items-center justify-center text-white text-xs font-medium mr-3\">\n            {card.author.avatar ? (\n              <Image\n                src={card.author.avatar}\n                alt={card.author.name}\n                width={32}\n                height={32}\n                className=\"rounded-full\"\n              />\n            ) : (\n              card.author.initials\n            )}\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <div className=\"text-xs font-medium text-light-h3 dark:text-dark-h3 truncate\">\n              {card.author.name}\n            </div>\n            <div className=\"text-xs text-light-secondary dark:text-dark-secondary truncate\">\n              {card.author.role}\n            </div>\n          </div>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n\nexport function TestimonialWall() {\n  return (\n    <section id=\"testimonials\" className=\"py-20 bg-light-background dark:bg-dark-background relative overflow-hidden\">\n      {/* Fade overlays */}\n      <div className=\"absolute top-0 left-0 right-0 h-20 bg-gradient-to-b from-light-background dark:from-dark-background to-transparent z-10\" />\n      <div className=\"absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-light-background dark:from-dark-background to-transparent z-10\" />\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <span className=\"inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4\">\n            💬 Client Love\n          </span>\n          \n          <h2 className=\"text-4xl sm:text-5xl font-bold gradient-text mb-6\">\n            What People Are Saying\n          </h2>\n          \n          <p className=\"text-lg text-light-secondary dark:text-dark-secondary max-w-3xl mx-auto\">\n            Real feedback from real clients who've experienced the transformative power of AI automation.\n          </p>\n        </motion.div>\n\n        {/* Testimonial Mosaic */}\n        <div className=\"relative\">\n          {/* Left sliding column */}\n          <motion.div\n            animate={{ y: [0, -50, 0] }}\n            transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n            className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\"\n          >\n            {infiniteCards.slice(0, 8).map((card, index) => (\n              <TestimonialCard key={`${card.id}-${index}`} card={card} index={index} />\n            ))}\n          </motion.div>\n\n          {/* Right sliding column (offset) */}\n          <motion.div\n            animate={{ y: [-50, 0, -50] }}\n            transition={{ duration: 25, repeat: Infinity, ease: \"linear\" }}\n            className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6\"\n          >\n            {infiniteCards.slice(4, 12).map((card, index) => (\n              <TestimonialCard key={`${card.id}-offset-${index}`} card={card} index={index} />\n            ))}\n          </motion.div>\n        </div>\n\n        {/* Stats Summary */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 pt-16 border-t border-light-primary/20 dark:border-dark-primary/20\"\n        >\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">150+</div>\n            <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Projects Completed</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">50+</div>\n            <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Happy Clients</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">98%</div>\n            <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Success Rate</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold gradient-text mb-2\">5+</div>\n            <div className=\"text-sm text-light-secondary dark:text-dark-secondary\">Years Experience</div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAOA,MAAM,mBAAsC;IAC1C;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;YACP,MAAM;QACR;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;YACP,MAAM;YACN,QAAQ;QACV;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;YACP,MAAM;QACR;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;QACA,OAAO;YACL,WAAW;QACb;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;YACP,OAAO;QACT;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;YACP,MAAM;YACN,QAAQ;QACV;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;YACP,MAAM;QACR;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;YACP,MAAM;YACN,QAAQ;QACV;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,UAAU;QACZ;IACF;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;YACP,MAAM;QACR;QACA,QAAQ;YACN,MAAM;YACN,MAAM;YACN,QAAQ;YACR,UAAU;QACZ;IACF;CACD;AAED,6CAA6C;AAC7C,MAAM,gBAAgB;OAAI;OAAqB;OAAqB;CAAiB;AAErF,SAAS,gBAAgB,EAAE,IAAI,EAAE,KAAK,EAA4C;IAChF,MAAM,cAAc;QAClB,OAAO;QACP,QAAQ;QACR,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,MAAM;QACN,OAAO;IACT;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAI;QAChD,UAAU;YAAE,MAAM;QAAK;QACvB,WAAW,CAAC;QACV,EAAE,WAAW,CAAC,KAAK,IAAI,CAAC,CAAC;QACzB,EAAE,KAAK,MAAM,GAAG,aAAa,CAAC,KAAK,MAAM,CAAC,GAAG,iFAAiF;;MAEhI,CAAC;;0BAGD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;oBAEZ,KAAK,IAAI,KAAK,wBACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,KAAK,OAAO,CAAC,IAAI;;;;;;0CAEpB,8OAAC;gCAAI,WAAU;0CACZ,KAAK,OAAO,CAAC,MAAM;;;;;;;;;;;;oBAKzB,KAAK,IAAI,KAAK,wBACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAA2E;gCACpF,KAAK,OAAO,CAAC,IAAI;gCAAC;;;;;;;;;;;;oBAKzB,KAAK,IAAI,KAAK,yBACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAA0D;;;;;;0CACzE,8OAAC;gCAAE,WAAU;0CACV,KAAK,OAAO,CAAC,KAAK;;;;;;;;;;;;oBAKxB,KAAK,IAAI,KAAK,yBACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,KAAK,KAAK,EAAE,aAAa;wCAC9B,KAAI;wCACJ,IAAI;wCACJ,WAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGpB,8OAAC;gCAAE,WAAU;0CACV,KAAK,OAAO,CAAC,IAAI;;;;;;;;;;;;kCAMxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,KAAK,MAAM,CAAC,MAAM,iBACjB,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,KAAK,MAAM,CAAC,MAAM;oCACvB,KAAK,KAAK,MAAM,CAAC,IAAI;oCACrB,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;2CAGZ,KAAK,MAAM,CAAC,QAAQ;;;;;;0CAGxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,KAAK,MAAM,CAAC,IAAI;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;kDACZ,KAAK,MAAM,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B;AAEO,SAAS;IACd,qBACE,8OAAC;QAAQ,IAAG;QAAe,WAAU;;0BAEnC,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAK,WAAU;0CAAqJ;;;;;;0CAIrK,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAIlE,8OAAC;gCAAE,WAAU;0CAA0E;;;;;;;;;;;;kCAMzF,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG,CAAC;wCAAI;qCAAE;gCAAC;gCAC1B,YAAY;oCAAE,UAAU;oCAAI,QAAQ;oCAAU,MAAM;gCAAS;gCAC7D,WAAU;0CAET,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACpC,8OAAC;wCAA4C,MAAM;wCAAM,OAAO;uCAA1C,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;0CAK/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC,CAAC;wCAAI;wCAAG,CAAC;qCAAG;gCAAC;gCAC5B,YAAY;oCAAE,UAAU;oCAAI,QAAQ;oCAAU,MAAM;gCAAS;gCAC7D,WAAU;0CAET,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,sBACrC,8OAAC;wCAAmD,MAAM;wCAAM,OAAO;uCAAjD,GAAG,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO;;;;;;;;;;;;;;;;kCAMxD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAwD;;;;;;;;;;;;0CAEzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAwD;;;;;;;;;;;;0CAEzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAwD;;;;;;;;;;;;0CAEzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnF", "debugId": null}}, {"offset": {"line": 2591, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/FAQ.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Plus, Minus, HelpCircle } from 'lucide-react';\nimport type { FAQItem } from '@/types';\n\nconst faqData: FAQItem[] = [\n  {\n    id: '1',\n    question: 'How do you approach AI and automation projects?',\n    answer: 'I start with a comprehensive analysis of your current processes to identify automation opportunities. Then I design custom AI solutions that integrate seamlessly with your existing systems, ensuring minimal disruption while maximizing efficiency gains. Every project includes thorough testing, training, and ongoing support.',\n    category: 'Process'\n  },\n  {\n    id: '2',\n    question: 'What makes your AI solutions different from others?',\n    answer: 'My solutions are built with a focus on practical business outcomes rather than just technical complexity. I combine cutting-edge AI technologies with deep business understanding, ensuring that every automation delivers measurable ROI. Plus, I provide comprehensive training and documentation so your team can confidently manage the systems.',\n    category: 'Approach'\n  },\n  {\n    id: '3',\n    question: 'How long does a typical AI automation project take?',\n    answer: 'Project timelines vary based on complexity, but most automation projects are completed within 2-8 weeks. Simple workflow automations can be delivered in 1-2 weeks, while complex AI systems may take 2-3 months. I always provide detailed timelines upfront and keep you updated throughout the process.',\n    category: 'Timeline'\n  },\n  {\n    id: '4',\n    question: 'Do you provide ongoing support after project completion?',\n    answer: 'Absolutely! I offer comprehensive post-launch support including system monitoring, performance optimization, user training, and technical assistance. Most clients choose ongoing maintenance packages to ensure their AI systems continue performing optimally as their business grows.',\n    category: 'Support'\n  },\n  {\n    id: '5',\n    question: 'Can you work with international clients and different time zones?',\n    answer: 'Yes, I work with clients globally and am experienced in managing projects across different time zones. I use collaborative tools and maintain flexible communication schedules to ensure smooth project delivery regardless of location. Many of my successful projects have been with international teams.',\n    category: 'International'\n  },\n  {\n    id: '6',\n    question: 'What industries do you specialize in?',\n    answer: 'I have experience across various industries including e-commerce, healthcare, finance, marketing agencies, and SaaS companies. My approach is industry-agnostic - I focus on understanding your specific business processes and challenges to create tailored solutions that work for your unique context.',\n    category: 'Industries'\n  },\n  {\n    id: '7',\n    question: 'How do you ensure data security and privacy?',\n    answer: 'Data security is paramount in all my projects. I implement industry-standard encryption, secure API connections, and follow best practices for data handling. All solutions are designed with privacy by design principles, and I can work within your existing security frameworks and compliance requirements.',\n    category: 'Security'\n  },\n  {\n    id: '8',\n    question: 'What\\'s the typical ROI for AI automation projects?',\n    answer: 'Most clients see ROI within 3-6 months, with typical returns ranging from 200-500% in the first year. The exact ROI depends on the processes being automated, but common benefits include 40-80% time savings, reduced errors, improved customer satisfaction, and the ability to scale operations without proportional staff increases.',\n    category: 'ROI'\n  }\n];\n\ninterface FAQItemProps {\n  faq: FAQItem;\n  isOpen: boolean;\n  onToggle: () => void;\n  index: number;\n}\n\nfunction FAQItemComponent({ faq, isOpen, onToggle, index }: FAQItemProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.6, delay: index * 0.1 }}\n      viewport={{ once: true }}\n      className=\"bg-white dark:bg-dark-card rounded-2xl shadow-card hover:shadow-card-hover transition-all duration-300\"\n    >\n      <button\n        onClick={onToggle}\n        className=\"w-full p-6 text-left flex items-center justify-between hover:bg-light-primary/5 dark:hover:bg-dark-primary/5 rounded-2xl transition-colors duration-300\"\n      >\n        <h3 className=\"text-lg font-semibold text-light-h2 dark:text-dark-h2 pr-4\">\n          {faq.question}\n        </h3>\n        <div className=\"flex-shrink-0\">\n          {isOpen ? (\n            <Minus className=\"w-6 h-6 text-light-primary dark:text-dark-primary\" />\n          ) : (\n            <Plus className=\"w-6 h-6 text-light-primary dark:text-dark-primary\" />\n          )}\n        </div>\n      </button>\n      \n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ height: 0, opacity: 0 }}\n            animate={{ height: 'auto', opacity: 1 }}\n            exit={{ height: 0, opacity: 0 }}\n            transition={{ duration: 0.3, ease: 'easeInOut' }}\n            className=\"overflow-hidden\"\n          >\n            <div className=\"px-6 pb-6\">\n              <div className=\"pt-2 border-t border-light-primary/10 dark:border-dark-primary/10\">\n                <p className=\"text-light-body dark:text-dark-body leading-relaxed\">\n                  {faq.answer}\n                </p>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.div>\n  );\n}\n\nexport function FAQ() {\n  const [openItems, setOpenItems] = useState<Set<string>>(new Set());\n\n  const toggleItem = (id: string) => {\n    const newOpenItems = new Set(openItems);\n    if (newOpenItems.has(id)) {\n      newOpenItems.delete(id);\n    } else {\n      newOpenItems.add(id);\n    }\n    setOpenItems(newOpenItems);\n  };\n\n  const categories = Array.from(new Set(faqData.map(faq => faq.category)));\n\n  return (\n    <section id=\"faq\" className=\"py-20 bg-light-background dark:bg-dark-background\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <span className=\"inline-block px-4 py-2 bg-light-primary/10 dark:bg-dark-primary/10 text-light-primary dark:text-dark-primary rounded-full text-sm font-medium mb-4\">\n            ❓ Got Questions?\n          </span>\n          \n          <h2 className=\"text-4xl sm:text-5xl font-bold gradient-text mb-6\">\n            Frequently Asked Questions\n          </h2>\n          \n          <p className=\"text-lg text-light-secondary dark:text-dark-secondary max-w-3xl mx-auto\">\n            Everything you need to know about working with me and my AI automation services. \n            Can't find what you're looking for? Feel free to reach out!\n          </p>\n        </motion.div>\n\n        {/* FAQ Categories */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          viewport={{ once: true }}\n          className=\"flex flex-wrap justify-center gap-3 mb-12\"\n        >\n          {categories.map((category) => (\n            <span\n              key={category}\n              className=\"px-4 py-2 bg-light-card dark:bg-dark-card text-light-secondary dark:text-dark-secondary rounded-full text-sm border border-light-primary/20 dark:border-dark-primary/20\"\n            >\n              {category}\n            </span>\n          ))}\n        </motion.div>\n\n        {/* FAQ Items */}\n        <div className=\"space-y-4\">\n          {faqData.map((faq, index) => (\n            <FAQItemComponent\n              key={faq.id}\n              faq={faq}\n              isOpen={openItems.has(faq.id)}\n              onToggle={() => toggleItem(faq.id)}\n              index={index}\n            />\n          ))}\n        </div>\n\n        {/* Contact CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16 pt-16 border-t border-light-primary/20 dark:border-dark-primary/20\"\n        >\n          <div className=\"bg-gradient-to-r from-light-primary/10 to-light-primary/5 dark:from-dark-primary/10 dark:to-dark-primary/5 rounded-3xl p-8\">\n            <HelpCircle className=\"w-12 h-12 text-light-primary dark:text-dark-primary mx-auto mb-4\" />\n            <h3 className=\"text-2xl font-bold text-light-h2 dark:text-dark-h2 mb-4\">\n              Still Have Questions?\n            </h3>\n            <p className=\"text-light-secondary dark:text-dark-secondary mb-6 max-w-2xl mx-auto\">\n              I'm here to help! Whether you need clarification on my services or want to discuss \n              your specific project requirements, don't hesitate to reach out.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"inline-flex items-center px-8 py-4 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-semibold rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105\">\n                Schedule a Call\n              </button>\n              <button className=\"inline-flex items-center px-8 py-4 bg-light-card dark:bg-dark-card border-2 border-light-primary dark:border-dark-primary text-light-primary dark:text-dark-primary font-semibold rounded-lg hover:bg-light-primary dark:hover:bg-dark-primary hover:text-white transition-all duration-300\">\n                Send a Message\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAJA;;;;;AAOA,MAAM,UAAqB;IACzB;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;CACD;AASD,SAAS,iBAAiB,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAgB;IACtE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAI;QAChD,UAAU;YAAE,MAAM;QAAK;QACvB,WAAU;;0BAEV,8OAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,8OAAC;wBAAG,WAAU;kCACX,IAAI,QAAQ;;;;;;kCAEf,8OAAC;wBAAI,WAAU;kCACZ,uBACC,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;iDAEjB,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKtB,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,QAAQ;wBAAG,SAAS;oBAAE;oBACjC,SAAS;wBAAE,QAAQ;wBAAQ,SAAS;oBAAE;oBACtC,MAAM;wBAAE,QAAQ;wBAAG,SAAS;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAY;oBAC/C,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7B;AAEO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAE5D,MAAM,aAAa,CAAC;QAClB,MAAM,eAAe,IAAI,IAAI;QAC7B,IAAI,aAAa,GAAG,CAAC,KAAK;YACxB,aAAa,MAAM,CAAC;QACtB,OAAO;YACL,aAAa,GAAG,CAAC;QACnB;QACA,aAAa;IACf;IAEA,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAA,MAAO,IAAI,QAAQ;IAErE,qBACE,8OAAC;QAAQ,IAAG;QAAM,WAAU;kBAC1B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAK,WAAU;sCAAqJ;;;;;;sCAIrK,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAIlE,8OAAC;4BAAE,WAAU;sCAA0E;;;;;;;;;;;;8BAOzF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;4BAEC,WAAU;sCAET;2BAHI;;;;;;;;;;8BASX,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,KAAK,sBACjB,8OAAC;4BAEC,KAAK;4BACL,QAAQ,UAAU,GAAG,CAAC,IAAI,EAAE;4BAC5B,UAAU,IAAM,WAAW,IAAI,EAAE;4BACjC,OAAO;2BAJF,IAAI,EAAE;;;;;;;;;;8BAUjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8NAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAG,WAAU;0CAA0D;;;;;;0CAGxE,8OAAC;gCAAE,WAAU;0CAAuE;;;;;;0CAIpF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;kDAAkM;;;;;;kDAGpN,8OAAC;wCAAO,WAAU;kDAA8R;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9T", "debugId": null}}, {"offset": {"line": 2980, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState } from 'react';\nimport { Mail, Phone, MapPin, Send, Linkedin, Twitter, Github, Globe, ArrowUp } from 'lucide-react';\nimport { useForm } from 'react-hook-form';\nimport toast from 'react-hot-toast';\n\ninterface NewsletterFormData {\n  email: string;\n}\n\nconst quickLinks = [\n  { label: 'Home', href: '#home' },\n  { label: 'Services', href: '#services' },\n  { label: 'Portfolio', href: '#portfolio' },\n  { label: 'About', href: '#story' },\n  { label: 'Case Studies', href: '#case-studies' },\n  { label: 'Testimonials', href: '#testimonials' },\n];\n\nconst resources = [\n  { label: 'Blog', href: '#insights' },\n  { label: 'AI Insights', href: '#insights' },\n  { label: 'Automation Guide', href: '#' },\n  { label: 'Best Practices', href: '#' },\n  { label: 'Industry Reports', href: '#' },\n  { label: 'Whitepapers', href: '#' },\n];\n\nconst support = [\n  { label: 'FAQ', href: '#faq' },\n  { label: 'Contact', href: '#contact' },\n  { label: 'Book a Call', href: '#' },\n  { label: 'Project Inquiry', href: '#' },\n  { label: 'Technical Support', href: '#' },\n  { label: 'Documentation', href: '#' },\n];\n\nconst socialLinks = [\n  { name: 'LinkedIn', icon: Linkedin, href: 'https://linkedin.com/in/denis-ai-specialist', color: 'hover:text-blue-600' },\n  { name: 'Twitter', icon: Twitter, href: 'https://twitter.com/denis_ai', color: 'hover:text-blue-400' },\n  { name: 'GitHub', icon: Github, href: 'https://github.com/denis-ai', color: 'hover:text-gray-600' },\n  { name: 'Website', icon: Globe, href: 'https://denis-portfolio.com', color: 'hover:text-green-600' },\n];\n\nexport function Footer() {\n  const { register, handleSubmit, formState: { errors }, reset } = useForm<NewsletterFormData>();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const handleNewsletterSubmit = async (data: NewsletterFormData) => {\n    setIsSubmitting(true);\n    try {\n      // Simulate newsletter subscription\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      toast.success('Successfully subscribed to newsletter!');\n      reset();\n    } catch (error) {\n      toast.error('Failed to subscribe. Please try again.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <footer className=\"bg-light-card dark:bg-dark-card border-t border-light-primary/20 dark:border-dark-primary/20\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid lg:grid-cols-4 md:grid-cols-2 gap-8\">\n            {/* Brand Section */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n              viewport={{ once: true }}\n              className=\"lg:col-span-1\"\n            >\n              <div className=\"mb-6\">\n                <h3 className=\"text-2xl font-bold gradient-text mb-4\">DENIS</h3>\n                <p className=\"text-light-body dark:text-dark-body leading-relaxed mb-6\">\n                  Transforming businesses worldwide with cutting-edge AI and automation solutions. \n                  From Kenya to global success, delivering results that matter.\n                </p>\n              </div>\n\n              {/* Contact Info */}\n              <div className=\"space-y-3\">\n                <div className=\"flex items-center text-light-secondary dark:text-dark-secondary\">\n                  <Mail className=\"w-4 h-4 mr-3 text-light-primary dark:text-dark-primary\" />\n                  <span className=\"text-sm\"><EMAIL></span>\n                </div>\n                <div className=\"flex items-center text-light-secondary dark:text-dark-secondary\">\n                  <Phone className=\"w-4 h-4 mr-3 text-light-primary dark:text-dark-primary\" />\n                  <span className=\"text-sm\">+254 700 000 000</span>\n                </div>\n                <div className=\"flex items-center text-light-secondary dark:text-dark-secondary\">\n                  <MapPin className=\"w-4 h-4 mr-3 text-light-primary dark:text-dark-primary\" />\n                  <span className=\"text-sm\">Nairobi, Kenya</span>\n                </div>\n              </div>\n\n              {/* Social Links */}\n              <div className=\"flex space-x-4 mt-6\">\n                {socialLinks.map((social) => (\n                  <a\n                    key={social.name}\n                    href={social.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className={`p-2 rounded-lg bg-light-background dark:bg-dark-background text-light-secondary dark:text-dark-secondary ${social.color} transition-all duration-300 hover:scale-110`}\n                    aria-label={social.name}\n                  >\n                    <social.icon className=\"w-5 h-5\" />\n                  </a>\n                ))}\n              </div>\n            </motion.div>\n\n            {/* Quick Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.1 }}\n              viewport={{ once: true }}\n            >\n              <h4 className=\"text-lg font-semibold text-light-h3 dark:text-dark-h3 mb-6\">\n                Quick Links\n              </h4>\n              <ul className=\"space-y-3\">\n                {quickLinks.map((link) => (\n                  <li key={link.label}>\n                    <button\n                      onClick={() => scrollToSection(link.href)}\n                      className=\"text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.label}\n                    </button>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Resources */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.2 }}\n              viewport={{ once: true }}\n            >\n              <h4 className=\"text-lg font-semibold text-light-h3 dark:text-dark-h3 mb-6\">\n                Resources\n              </h4>\n              <ul className=\"space-y-3\">\n                {resources.map((link) => (\n                  <li key={link.label}>\n                    <button\n                      onClick={() => scrollToSection(link.href)}\n                      className=\"text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary transition-colors duration-300 text-sm\"\n                    >\n                      {link.label}\n                    </button>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n\n            {/* Newsletter Signup */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.3 }}\n              viewport={{ once: true }}\n            >\n              <h4 className=\"text-lg font-semibold text-light-h3 dark:text-dark-h3 mb-6\">\n                Stay Updated\n              </h4>\n              <p className=\"text-light-secondary dark:text-dark-secondary text-sm mb-4\">\n                Get the latest insights on AI and automation delivered to your inbox.\n              </p>\n              \n              <form onSubmit={handleSubmit(handleNewsletterSubmit)} className=\"space-y-3\">\n                <div>\n                  <input\n                    {...register('email', {\n                      required: 'Email is required',\n                      pattern: {\n                        value: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n                        message: 'Please enter a valid email'\n                      }\n                    })}\n                    type=\"email\"\n                    placeholder=\"Enter your email\"\n                    className=\"w-full px-4 py-3 rounded-lg border border-light-primary/20 dark:border-dark-primary/20 bg-light-background dark:bg-dark-background text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors text-sm\"\n                  />\n                  {errors.email && (\n                    <p className=\"mt-1 text-xs text-red-500\">{errors.email.message}</p>\n                  )}\n                </div>\n                \n                <button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className=\"w-full flex items-center justify-center px-4 py-3 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-medium rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed text-sm\"\n                >\n                  {isSubmitting ? (\n                    'Subscribing...'\n                  ) : (\n                    <>\n                      Subscribe\n                      <Send className=\"ml-2 w-4 h-4\" />\n                    </>\n                  )}\n                </button>\n              </form>\n\n              {/* Support Links */}\n              <div className=\"mt-8\">\n                <h5 className=\"text-sm font-medium text-light-h3 dark:text-dark-h3 mb-3\">\n                  Support\n                </h5>\n                <ul className=\"space-y-2\">\n                  {support.slice(0, 3).map((link) => (\n                    <li key={link.label}>\n                      <button\n                        onClick={() => scrollToSection(link.href)}\n                        className=\"text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary transition-colors duration-300 text-xs\"\n                      >\n                        {link.label}\n                      </button>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n\n        {/* Bottom Footer */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"py-8 border-t border-light-primary/20 dark:border-dark-primary/20\"\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"text-light-secondary dark:text-dark-secondary text-sm mb-4 md:mb-0\">\n              © 2024 Denis AI & Automation Specialist. All rights reserved.\n            </div>\n            \n            <div className=\"flex items-center space-x-6\">\n              <button\n                onClick={() => scrollToSection('#')}\n                className=\"text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary transition-colors duration-300 text-sm\"\n              >\n                Privacy Policy\n              </button>\n              <button\n                onClick={() => scrollToSection('#')}\n                className=\"text-light-secondary dark:text-dark-secondary hover:text-light-primary dark:hover:text-dark-primary transition-colors duration-300 text-sm\"\n              >\n                Terms of Service\n              </button>\n              <button\n                onClick={scrollToTop}\n                className=\"p-2 rounded-lg bg-light-primary dark:bg-dark-primary text-white hover:shadow-neon transition-all duration-300 hover:scale-110\"\n                aria-label=\"Back to top\"\n              >\n                <ArrowUp className=\"w-4 h-4\" />\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AAYA,MAAM,aAAa;IACjB;QAAE,OAAO;QAAQ,MAAM;IAAQ;IAC/B;QAAE,OAAO;QAAY,MAAM;IAAY;IACvC;QAAE,OAAO;QAAa,MAAM;IAAa;IACzC;QAAE,OAAO;QAAS,MAAM;IAAS;IACjC;QAAE,OAAO;QAAgB,MAAM;IAAgB;IAC/C;QAAE,OAAO;QAAgB,MAAM;IAAgB;CAChD;AAED,MAAM,YAAY;IAChB;QAAE,OAAO;QAAQ,MAAM;IAAY;IACnC;QAAE,OAAO;QAAe,MAAM;IAAY;IAC1C;QAAE,OAAO;QAAoB,MAAM;IAAI;IACvC;QAAE,OAAO;QAAkB,MAAM;IAAI;IACrC;QAAE,OAAO;QAAoB,MAAM;IAAI;IACvC;QAAE,OAAO;QAAe,MAAM;IAAI;CACnC;AAED,MAAM,UAAU;IACd;QAAE,OAAO;QAAO,MAAM;IAAO;IAC7B;QAAE,OAAO;QAAW,MAAM;IAAW;IACrC;QAAE,OAAO;QAAe,MAAM;IAAI;IAClC;QAAE,OAAO;QAAmB,MAAM;IAAI;IACtC;QAAE,OAAO;QAAqB,MAAM;IAAI;IACxC;QAAE,OAAO;QAAiB,MAAM;IAAI;CACrC;AAED,MAAM,cAAc;IAClB;QAAE,MAAM;QAAY,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAA+C,OAAO;IAAsB;IACtH;QAAE,MAAM;QAAW,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAgC,OAAO;IAAsB;IACrG;QAAE,MAAM;QAAU,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAA+B,OAAO;IAAsB;IAClG;QAAE,MAAM;QAAW,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAA+B,OAAO;IAAuB;CACpG;AAEM,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,yBAAyB,OAAO;QACpC,gBAAgB;QAChB,IAAI;YACF,mCAAmC;YACnC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,8OAAC;gDAAE,WAAU;0DAA2D;;;;;;;;;;;;kDAO1E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;0DAE5B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;;kDAK9B,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;gDAEC,MAAM,OAAO,IAAI;gDACjB,QAAO;gDACP,KAAI;gDACJ,WAAW,CAAC,yGAAyG,EAAE,OAAO,KAAK,CAAC,4CAA4C,CAAC;gDACjL,cAAY,OAAO,IAAI;0DAEvB,cAAA,8OAAC,OAAO,IAAI;oDAAC,WAAU;;;;;;+CAPlB,OAAO,IAAI;;;;;;;;;;;;;;;;0CAcxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAA6D;;;;;;kDAG3E,8OAAC;wCAAG,WAAU;kDACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;0DACC,cAAA,8OAAC;oDACC,SAAS,IAAM,gBAAgB,KAAK,IAAI;oDACxC,WAAU;8DAET,KAAK,KAAK;;;;;;+CALN,KAAK,KAAK;;;;;;;;;;;;;;;;0CAazB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAA6D;;;;;;kDAG3E,8OAAC;wCAAG,WAAU;kDACX,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC;0DACC,cAAA,8OAAC;oDACC,SAAS,IAAM,gBAAgB,KAAK,IAAI;oDACxC,WAAU;8DAET,KAAK,KAAK;;;;;;+CALN,KAAK,KAAK;;;;;;;;;;;;;;;;0CAazB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,8OAAC;wCAAG,WAAU;kDAA6D;;;;;;kDAG3E,8OAAC;wCAAE,WAAU;kDAA6D;;;;;;kDAI1E,8OAAC;wCAAK,UAAU,aAAa;wCAAyB,WAAU;;0DAC9D,8OAAC;;kEACC,8OAAC;wDACE,GAAG,SAAS,SAAS;4DACpB,UAAU;4DACV,SAAS;gEACP,OAAO;gEACP,SAAS;4DACX;wDACF,EAAE;wDACF,MAAK;wDACL,aAAY;wDACZ,WAAU;;;;;;oDAEX,OAAO,KAAK,kBACX,8OAAC;wDAAE,WAAU;kEAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;0DAIlE,8OAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,eACC,iCAEA;;wDAAE;sEAEA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;kDAOxB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2D;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBACxB,8OAAC;kEACC,cAAA,8OAAC;4DACC,SAAS,IAAM,gBAAgB,KAAK,IAAI;4DACxC,WAAU;sEAET,KAAK,KAAK;;;;;;uDALN,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAgB/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAqE;;;;;;0CAIpF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,SAAS;wCACT,WAAU;wCACV,cAAW;kDAEX,cAAA,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnC", "debugId": null}}, {"offset": {"line": 3658, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatDate(date: string | Date): string {\n  const d = new Date(date);\n  return d.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  });\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-');\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength).trim() + '...';\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function getRandomItems<T>(array: T[], count: number): T[] {\n  const shuffled = [...array].sort(() => 0.5 - Math.random());\n  return shuffled.slice(0, count);\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,IAAI,KAAK;AAC3C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,eAAkB,KAAU,EAAE,KAAa;IACzD,MAAM,WAAW;WAAI;KAAM,CAAC,IAAI,CAAC,IAAM,MAAM,KAAK,MAAM;IACxD,OAAO,SAAS,KAAK,CAAC,GAAG;AAC3B;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C", "debugId": null}}, {"offset": {"line": 3727, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/components/ContactModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X, Mail, Calendar, MessageSquare, Send, Phone } from 'lucide-react';\nimport { useForm } from 'react-hook-form';\nimport toast from 'react-hot-toast';\nimport { cn } from '@/lib/utils';\n\ninterface ContactModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\ninterface ContactFormData {\n  name: string;\n  email: string;\n  subject: string;\n  message: string;\n}\n\nexport function ContactModal({ isOpen, onClose }: ContactModalProps) {\n  const [activeTab, setActiveTab] = useState<'options' | 'form'>('options');\n  const { register, handleSubmit, formState: { errors }, reset } = useForm<ContactFormData>();\n\n  const handleEmailClick = () => {\n    window.open('mailto:<EMAIL>?subject=Let\\'s Connect!', '_blank');\n    onClose();\n  };\n\n  const handleCallClick = () => {\n    window.open('https://calendly.com/denis-ai-specialist/30min', '_blank');\n    onClose();\n  };\n\n  const handleFormSubmit = async (data: ContactFormData) => {\n    try {\n      // Simulate form submission\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      toast.success('Message sent successfully! I\\'ll get back to you soon.');\n      reset();\n      onClose();\n    } catch (error) {\n      toast.error('Failed to send message. Please try again.');\n    }\n  };\n\n  const contactOptions = [\n    {\n      id: 'email',\n      title: 'Send an Email',\n      description: 'Quick and direct communication',\n      icon: Mail,\n      action: handleEmailClick,\n      color: 'from-blue-500 to-cyan-500',\n    },\n    {\n      id: 'call',\n      title: 'Book a 30 Min Call',\n      description: 'Let\\'s discuss your project in detail',\n      icon: Calendar,\n      action: handleCallClick,\n      color: 'from-green-500 to-emerald-500',\n    },\n    {\n      id: 'form',\n      title: 'Contact Form',\n      description: 'Tell me about your needs',\n      icon: MessageSquare,\n      action: () => setActiveTab('form'),\n      color: 'from-purple-500 to-pink-500',\n    },\n  ];\n\n  if (!isOpen) return null;\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4\"\n        onClick={onClose}\n      >\n        <motion.div\n          initial={{ scale: 0.9, opacity: 0 }}\n          animate={{ scale: 1, opacity: 1 }}\n          exit={{ scale: 0.9, opacity: 0 }}\n          transition={{ type: \"spring\", duration: 0.5 }}\n          className=\"bg-white dark:bg-dark-card rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-hidden\"\n          onClick={(e) => e.stopPropagation()}\n        >\n          {/* Header */}\n          <div className=\"relative p-6 border-b border-gray-200 dark:border-gray-700\">\n            <button\n              onClick={onClose}\n              className=\"absolute top-4 right-4 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors\"\n            >\n              <X className=\"w-5 h-5 text-gray-500 dark:text-gray-400\" />\n            </button>\n            \n            <div className=\"text-center\">\n              <h2 className=\"text-2xl font-bold text-light-h2 dark:text-dark-h2 mb-2\">\n                We'd Love to Hear From You!\n              </h2>\n              <p className=\"text-light-secondary dark:text-dark-secondary\">\n                {activeTab === 'options' \n                  ? \"Choose how you'd like to connect:\" \n                  : \"Tell us about your project\"\n                }\n              </p>\n            </div>\n          </div>\n\n          {/* Content */}\n          <div className=\"p-6\">\n            {activeTab === 'options' ? (\n              <div className=\"space-y-4\">\n                {contactOptions.map((option, index) => (\n                  <motion.button\n                    key={option.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    onClick={option.action}\n                    className=\"w-full p-4 rounded-xl border-2 border-gray-200 dark:border-gray-700 hover:border-light-primary dark:hover:border-dark-primary transition-all duration-300 hover:shadow-lg group\"\n                  >\n                    <div className=\"flex items-center space-x-4\">\n                      <div className={cn(\n                        \"w-12 h-12 rounded-lg bg-gradient-to-br flex items-center justify-center\",\n                        option.color\n                      )}>\n                        <option.icon className=\"w-6 h-6 text-white\" />\n                      </div>\n                      <div className=\"flex-1 text-left\">\n                        <h3 className=\"font-semibold text-light-h3 dark:text-dark-h3 group-hover:text-light-primary dark:group-hover:text-dark-primary transition-colors\">\n                          {option.title}\n                        </h3>\n                        <p className=\"text-sm text-light-secondary dark:text-dark-secondary\">\n                          {option.description}\n                        </p>\n                      </div>\n                    </div>\n                  </motion.button>\n                ))}\n              </div>\n            ) : (\n              <motion.form\n                initial={{ opacity: 0, x: 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                onSubmit={handleSubmit(handleFormSubmit)}\n                className=\"space-y-4\"\n              >\n                <button\n                  type=\"button\"\n                  onClick={() => setActiveTab('options')}\n                  className=\"flex items-center text-sm text-light-primary dark:text-dark-primary hover:underline mb-4\"\n                >\n                  ← Back to options\n                </button>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-light-body dark:text-dark-body mb-2\">\n                    Name *\n                  </label>\n                  <input\n                    {...register('name', { required: 'Name is required' })}\n                    className=\"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors\"\n                    placeholder=\"Your full name\"\n                  />\n                  {errors.name && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.name.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-light-body dark:text-dark-body mb-2\">\n                    Email *\n                  </label>\n                  <input\n                    {...register('email', { \n                      required: 'Email is required',\n                      pattern: {\n                        value: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n                        message: 'Please enter a valid email'\n                      }\n                    })}\n                    type=\"email\"\n                    className=\"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                  {errors.email && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.email.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-light-body dark:text-dark-body mb-2\">\n                    Subject *\n                  </label>\n                  <input\n                    {...register('subject', { required: 'Subject is required' })}\n                    className=\"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors\"\n                    placeholder=\"What's this about?\"\n                  />\n                  {errors.subject && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.subject.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-light-body dark:text-dark-body mb-2\">\n                    Message *\n                  </label>\n                  <textarea\n                    {...register('message', { required: 'Message is required' })}\n                    rows={4}\n                    className=\"w-full px-4 py-3 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-light-body dark:text-dark-body focus:ring-2 focus:ring-light-primary dark:focus:ring-dark-primary focus:border-transparent transition-colors resize-none\"\n                    placeholder=\"Tell me about your project or how I can help...\"\n                  />\n                  {errors.message && (\n                    <p className=\"mt-1 text-sm text-red-500\">{errors.message.message}</p>\n                  )}\n                </div>\n\n                <button\n                  type=\"submit\"\n                  className=\"w-full flex items-center justify-center space-x-2 px-6 py-3 bg-gradient-primary-button dark:bg-gradient-primary-button text-white font-medium rounded-lg hover:shadow-neon transition-all duration-300 hover:scale-105\"\n                >\n                  <Send className=\"w-4 h-4\" />\n                  <span>Send Message</span>\n                </button>\n              </motion.form>\n            )}\n          </div>\n        </motion.div>\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AAqBO,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO,EAAqB;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/D,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IAEvE,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC,oDAAoD;QAChE;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC,kDAAkD;QAC9D;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,2BAA2B;YAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YACd;YACA;QACF,EAAE,OAAO,OAAO;YACd,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kMAAA,CAAA,OAAI;YACV,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,0MAAA,CAAA,WAAQ;YACd,QAAQ;YACR,OAAO;QACT;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,wNAAA,CAAA,gBAAa;YACnB,QAAQ,IAAM,aAAa;YAC3B,OAAO;QACT;KACD;IAED,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;sBAET,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAClC,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,MAAM;oBAAE,OAAO;oBAAK,SAAS;gBAAE;gBAC/B,YAAY;oBAAE,MAAM;oBAAU,UAAU;gBAAI;gBAC5C,WAAU;gBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAGjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0D;;;;;;kDAGxE,8OAAC;wCAAE,WAAU;kDACV,cAAc,YACX,sCACA;;;;;;;;;;;;;;;;;;kCAOV,8OAAC;wBAAI,WAAU;kCACZ,cAAc,0BACb,8OAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;oCACjC,SAAS,OAAO,MAAM;oCACtB,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,2EACA,OAAO,KAAK;0DAEZ,cAAA,8OAAC,OAAO,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,OAAO,KAAK;;;;;;kEAEf,8OAAC;wDAAE,WAAU;kEACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;mCAnBpB,OAAO,EAAE;;;;;;;;;iDA2BpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,UAAU,aAAa;4BACvB,WAAU;;8CAEV,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CACX;;;;;;8CAID,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAqE;;;;;;sDAGtF,8OAAC;4CACE,GAAG,SAAS,QAAQ;gDAAE,UAAU;4CAAmB,EAAE;4CACtD,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,IAAI,kBACV,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;8CAIjE,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAqE;;;;;;sDAGtF,8OAAC;4CACE,GAAG,SAAS,SAAS;gDACpB,UAAU;gDACV,SAAS;oDACP,OAAO;oDACP,SAAS;gDACX;4CACF,EAAE;4CACF,MAAK;4CACL,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,KAAK,kBACX,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAIlE,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAqE;;;;;;sDAGtF,8OAAC;4CACE,GAAG,SAAS,WAAW;gDAAE,UAAU;4CAAsB,EAAE;4CAC5D,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,OAAO,kBACb,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;8CAIpE,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAqE;;;;;;sDAGtF,8OAAC;4CACE,GAAG,SAAS,WAAW;gDAAE,UAAU;4CAAsB,EAAE;4CAC5D,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,OAAO,kBACb,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;8CAIpE,8OAAC;oCACC,MAAK;oCACL,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB", "debugId": null}}, {"offset": {"line": 4182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Project%20portfolio%20Aug/portfolio-website/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Header } from '@/components/Header';\nimport { Hero } from '@/components/Hero';\nimport { BlogSection } from '@/components/BlogSection';\nimport { PersonalStory } from '@/components/PersonalStory';\nimport { CaseStudyShowcase } from '@/components/CaseStudyShowcase';\nimport { TestimonialWall } from '@/components/TestimonialWall';\nimport { FAQ } from '@/components/FAQ';\nimport { Footer } from '@/components/Footer';\nimport { ContactModal } from '@/components/ContactModal';\n\nexport default function Home() {\n  const [isContactModalOpen, setIsContactModalOpen] = useState(false);\n\n  const handleContactClick = () => {\n    setIsContactModalOpen(true);\n  };\n\n  const handleContactClose = () => {\n    setIsContactModalOpen(false);\n  };\n\n  return (\n    <main className=\"min-h-screen bg-light-background dark:bg-dark-background text-light-body dark:text-dark-body\">\n      <Header onContactClick={handleContactClick} />\n\n      <Hero onContactClick={handleContactClick} />\n\n      <BlogSection />\n\n      <PersonalStory />\n\n      <CaseStudyShowcase />\n\n      <TestimonialWall />\n\n      <FAQ />\n\n      <Footer />\n\n      <ContactModal\n        isOpen={isContactModalOpen}\n        onClose={handleContactClose}\n      />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAae,SAAS;IACtB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,qBAAqB;QACzB,sBAAsB;IACxB;IAEA,MAAM,qBAAqB;QACzB,sBAAsB;IACxB;IAEA,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC,4HAAA,CAAA,SAAM;gBAAC,gBAAgB;;;;;;0BAExB,8OAAC,0HAAA,CAAA,OAAI;gBAAC,gBAAgB;;;;;;0BAEtB,8OAAC,iIAAA,CAAA,cAAW;;;;;0BAEZ,8OAAC,mIAAA,CAAA,gBAAa;;;;;0BAEd,8OAAC,uIAAA,CAAA,oBAAiB;;;;;0BAElB,8OAAC,qIAAA,CAAA,kBAAe;;;;;0BAEhB,8OAAC,yHAAA,CAAA,MAAG;;;;;0BAEJ,8OAAC,4HAAA,CAAA,SAAM;;;;;0BAEP,8OAAC,kIAAA,CAAA,eAAY;gBACX,QAAQ;gBACR,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}]}